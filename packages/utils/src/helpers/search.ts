/**
 * 多字段模糊搜索函数
 * @param data 要搜索的源数据数组
 * @param keyword 搜索关键词
 * @param keys 要搜索的字段名数组
 * @returns 过滤后的新数组
 */
export function multiFieldSearch<T extends Record<string, any>>(data: T[], keyword: string, keys: (keyof T)[]): T[] {
  // 1. 如果关键词为空，直接返回所有数据
  const trimmedKeyword = keyword.trim();
  if (!trimmedKeyword) {
    return data;
  }

  // 2. 将关键词转为小写，用于不区分大小写的比较
  const lowerCaseKeyword = trimmedKeyword.toLowerCase();

  // 3. 过滤数据
  return data.filter((item) => {
    // 4. 使用 .some() 遍历需要搜索的字段
    // 只要有一个字段匹配成功，.some() 就会返回 true，这个 item 就会被保留
    return keys.some((key) => {
      const value = item[key];

      // 5. 健壮性检查：确保值存在且能转换为字符串
      // String(value) 可以安全地处理字符串、数字等类型
      return value !== null && String(value).toLowerCase().includes(lowerCaseKeyword);
    });
  });
}
