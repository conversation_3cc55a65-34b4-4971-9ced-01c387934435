import type { ButtonInfo, ModuleInfo, UserInfo } from '@vben-core/typings';

import { acceptHMRUpdate, defineStore } from 'pinia';

interface AccessState {
  buttonList: ButtonInfo[];
  menuList: TargetMenuItem[];
  /**
   * 模块列表
   */
  moduleList: ModuleInfo[];
  /**
   * 用户信息
   */
  userInfo: null | UserInfo;
  /**
   * 用户角色
   */
  userRoles: string[];
}

// 原始数据类型
interface SourceMenuItem {
  [key: string]: any;
  children?: SourceMenuItem[];
  code: string;
  component: string;
  id: number;
  name: string;
  sortCode: number;
  urlAddress: string;
}

interface SourceApp {
  [key: string]: any;
  code: string;
  icon?: string;
  id: number;
  moduleList?: SourceMenuItem[];
  name: string;
  sortCode: number;
}

// 目标菜单项类型
interface TargetMenuItem {
  children?: TargetMenuItem[];
  component?: string;
  meta: {
    appCode?: string;
    icon?: string;
    keepAlive?: boolean;
    order: number;
    title: string;
  };
  name: string;
  path: string;
  redirect?: string;
}

// 转换后的应用层级路由结构
interface TransformedAppRoute {
  // 子路由/菜单就是转换后的 moduleList
  children: TargetMenuItem[];
  meta: {
    icon?: string;
    order: number;
    title: string;
  };
  // 路由名称，通常用 code
  name: string;
  // 通常，一个模块/应用会有一个根路径
  path: string;
  // 重定向到第一个子菜单
  redirect?: string;
  // 应用的访问地址
  url?: string;
}

/**
 * 将原始的应用菜单数据转换为分层的前端路由结构
 * @param {SourceApp[]} sourceData - 原始数据，包含一个或多个应用对象
 * @returns {TransformedAppRoute[]} - 转换后的、保留应用层级的路由数组
 */
function generateHierarchicalMenus(sourceData: SourceApp[]): TransformedAppRoute[] {
  // 内部辅助函数 transformMenuItem
  function transformMenuItem(item: SourceMenuItem): TargetMenuItem {
    const newItem: TargetMenuItem = {
      name: item.code,
      path: item.urlAddress || item.code,
      meta: { title: item.name, order: item.sortCode, icon: item.icon, appCode: item.appCode, keepAlive: true },
    };
    if (item.component) {
      let componentPath = item.component.replace('views/', '').replace('.vue', '');
      if (!componentPath.startsWith('/')) {
        componentPath = `/${componentPath}`;
      }
      newItem.component = componentPath;
    }
    if (item.children && item.children.length > 0) {
      newItem.children = item.children.map((element) => transformMenuItem(element));
      if (newItem.children[0]?.path) {
        newItem.redirect = newItem.children[0].path;
      }
    }
    return newItem;
  }

  if (!Array.isArray(sourceData)) {
    console.error('输入的数据必须是一个数组。');
    return [];
  }

  const result: TransformedAppRoute[] = sourceData.map((app) => {
    const transformedChildren = app.moduleList ? app.moduleList.map((element) => transformMenuItem(element)) : [];

    // 构建代表当前应用的顶级路由对象
    const appRoute: TransformedAppRoute = {
      path: `/${app.code}`,
      name: app.code,
      url: app.url,
      meta: {
        title: app.name,
        icon: app.icon,
        order: app.sortCode,
      },
      children: transformedChildren,
    };

    if (transformedChildren.length > 0 && transformedChildren[0]?.path) {
      const firstChildPath = transformedChildren[0].path;
      appRoute.redirect = firstChildPath.startsWith('/') ? firstChildPath : `${appRoute.path}/${firstChildPath}`;
    }

    return appRoute;
  });

  return result;
}

/**
 * @zh_CN 用户信息相关
 */
export const useUserStore = defineStore('core-user', {
  actions: {
    setUserInfo(userInfo: null | UserInfo) {
      // 设置用户信息
      this.userInfo = userInfo;
      // 设置角色信息
      const roles = userInfo?.roles ?? [];
      this.setUserRoles(roles);
    },
    setUserRoles(roles: string[]) {
      this.userRoles = roles;
    },
    setButtonList(buttonList: any[]) {
      this.buttonList = buttonList;
    },
    setModuleList(moduleList: any[]) {
      this.moduleList = moduleList;
      this.menuList = generateHierarchicalMenus(moduleList);
    },
  },
  state: (): AccessState => ({
    userInfo: null,
    buttonList: [],
    moduleList: [],
    userRoles: [],
    menuList: [],
  }),
});

// 解决热更新问题
const hot = import.meta.hot;
if (hot) {
  hot.accept(acceptHMRUpdate(useUserStore, hot));
}
