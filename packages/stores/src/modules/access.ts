import type { RouteRecordRaw } from 'vue-router';

import type { MenuRecordRaw } from '@vben-core/typings';

import { useCookies } from '@vueuse/integrations/useCookies';
import { acceptHMRUpdate, defineStore } from 'pinia';
import tld from 'tldjs';

type AccessToken = null | string;

interface AccessState {
  /**
   * 可访问的应用列表
   */
  accessApps: (MenuRecordRaw & { url: string })[];
  /**
   * 权限码
   */
  accessCodes: string[];
  /**
   * 可访问的菜单列表
   */
  accessMenus: MenuRecordRaw[];
  /**
   * 可访问的路由列表
   */
  accessRoutes: RouteRecordRaw[];
  /**
   * 登录 accessToken
   */
  accessToken: AccessToken;
  /**
   * 是否已经检查过权限
   */
  isAccessChecked: boolean;
  /**
   * 是否锁屏状态
   */
  isLockScreen: boolean;
  /**
   * 锁屏密码
   */
  lockScreenPassword?: string;
  /**
   * 登录是否过期
   */
  loginExpired: boolean;
  /**
   * 登录 accessToken
   */
  refreshToken: AccessToken;
}

// 将 localhost 设置为合法的域名
const tldjs = tld.fromUserSettings({
  validHosts: ['localhost'],
});
const env = import.meta.env.PROD ? 'prod' : 'dev';
const appVersion = import.meta.env.VITE_APP_VERSION;
const namespace = `${import.meta.env.VITE_PROJECT_NAMESPACE}-${appVersion}-${env}`;
const accessTokenKey = `${namespace}-accessToken`;
const refreshTokenKey = `${namespace}-refreshToken`;
const cookies = useCookies([accessTokenKey, refreshTokenKey]);
const getCookieDomain = (): string => {
  const r = /^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)(?:$|(?!\.$)\.)){4}$/;
  // 使用三元表达式，并确保 tldjs.getDomain() 返回 null 时有备用值
  return r.test(window.location.hostname)
    ? window.location.hostname
    : tldjs.getDomain(window.location.hostname) || window.location.hostname;
};
/**
 * @zh_CN 访问权限相关
 */
export const useAccessStore = defineStore('core-access', {
  actions: {
    getMenuByPath(path: string) {
      function findMenu(menus: MenuRecordRaw[], path: string): MenuRecordRaw | undefined {
        for (const menu of menus) {
          if (menu.path === path) {
            return menu;
          }
          if (menu.children) {
            const matched = findMenu(menu.children, path);
            if (matched) {
              return matched;
            }
          }
        }
      }
      return findMenu(this.accessMenus, path);
    },
    lockScreen(password: string) {
      this.isLockScreen = true;
      this.lockScreenPassword = password;
    },
    setAccessCodes(buttonList: any[]) {
      this.accessCodes = buttonList.map((item: any) => item.code);
    },
    setAccessApps(moduleList: any[]) {
      const apps: (MenuRecordRaw & { url: string })[] = [];
      moduleList.forEach((app: { code: string; name: string; url: string }) => {
        apps.push({
          path: app.code,
          name: app.name,
          url: app.url,
        });
      });
      this.accessApps = apps;
    },
    setAccessMenus(menus: MenuRecordRaw[]) {
      this.accessMenus = menus;
    },
    setAccessRoutes(routes: RouteRecordRaw[]) {
      this.accessRoutes = routes;
    },
    setAccessToken(token: AccessToken) {
      this.accessToken = token;
      if (token) {
        cookies.set(accessTokenKey, token, {
          domain: getCookieDomain(),
          path: '/',
          maxAge: 60 * 60 * 24 * 7,
        });
      } else {
        cookies.remove(accessTokenKey, {
          domain: getCookieDomain(),
          path: '/',
        });
      }
    },
    setIsAccessChecked(isAccessChecked: boolean) {
      this.isAccessChecked = isAccessChecked;
    },
    setLoginExpired(loginExpired: boolean) {
      this.loginExpired = loginExpired;
    },
    setRefreshToken(token: AccessToken) {
      this.refreshToken = token;
      if (token) {
        cookies.set(refreshTokenKey, token, {
          domain: getCookieDomain(),
          path: '/',
          maxAge: 60 * 60 * 24 * 30,
        });
      } else {
        cookies.remove(refreshTokenKey, {
          domain: getCookieDomain(),
          path: '/',
        });
      }
    },
    unlockScreen() {
      this.isLockScreen = false;
      this.lockScreenPassword = undefined;
    },
  },
  persist: {
    // 持久化
    pick: ['accessApps', 'accessCodes', 'isLockScreen', 'lockScreenPassword'],
  },
  state: (): AccessState => ({
    accessApps: [],
    accessCodes: [],
    accessMenus: [],
    accessRoutes: [],
    accessToken: cookies.get(accessTokenKey) || null,
    isAccessChecked: false,
    isLockScreen: false,
    lockScreenPassword: undefined,
    loginExpired: false,
    refreshToken: cookies.get(refreshTokenKey) || null,
  }),
});

// 解决热更新问题
const hot = import.meta.hot;
if (hot) {
  hot.accept(acceptHMRUpdate(useAccessStore, hot));
}
