import type { RequestClient } from './request-client';
import type { MakeErrorMessageFn, ResponseInterceptorConfig } from './types';

import { $t } from '@vben/locales';
import { isFunction } from '@vben/utils';

import axios from 'axios';

export const defaultResponseInterceptor = ({
  codeField = 'code',
  dataField = 'data',
  successCode = 200,
}: {
  codeField: string;
  dataField: ((response: any) => any) | string;
  successCode: ((code: any) => boolean) | number | string;
}): ResponseInterceptorConfig => {
  return {
    fulfilled: (response) => {
      const { config, data: responseData, status } = response;
      if (config.responseReturn === 'raw') {
        return response;
      }
      if (status >= 200 && status < 400) {
        if (config.responseReturn === 'body') {
          return responseData;
        } else if (
          isFunction(successCode) ? successCode(responseData[codeField]) : responseData[codeField] === successCode
        ) {
          return isFunction(dataField) ? dataField(responseData) : responseData[dataField];
        }
      }
      throw Object.assign({}, response, { response });
    },
  };
};

export const authenticateResponseInterceptor = ({
  client,
  doReAuthenticate,
  doRefreshToken,
  enableRefreshToken,
  formatToken,
  getIsReauthenticating = () => false,
  setIsReauthenticating = () => {},
}: {
  client: RequestClient;
  doReAuthenticate: () => Promise<void>;
  doRefreshToken: () => Promise<string>;
  enableRefreshToken: boolean;
  formatToken: (token: string) => null | string;
  getIsReauthenticating?: () => boolean;
  setIsReauthenticating?: (status: boolean) => void;
}): ResponseInterceptorConfig => {
  return {
    rejected: async (error) => {
      const { config, response } = error;

      if (response?.status !== 401) {
        throw error;
      }

      if (getIsReauthenticating()) {
        return new Promise(() => {});
      }

      setIsReauthenticating(true);

      if (!enableRefreshToken || config.__isRetryRequest) {
        try {
          await doReAuthenticate();
          return new Promise(() => {});
        } catch (error_) {
          setIsReauthenticating(false);
          throw error_;
        }
      }

      client.isRefreshing = true;
      config.__isRetryRequest = true;

      try {
        const newToken = await doRefreshToken();
        client.isRefreshing = false;
        setIsReauthenticating(false);
        client.refreshTokenQueue.forEach((callback) => callback(newToken));
        client.refreshTokenQueue = [];
        return client.request(error.config.url, { ...error.config });
      } catch {
        client.isRefreshing = false;
        setIsReauthenticating(false);
        client.refreshTokenQueue.forEach((callback) => callback(''));
        client.refreshTokenQueue = [];
        await doReAuthenticate();
        return new Promise(() => {});
      }
    },
  };
};

export const errorMessageResponseInterceptor = (makeErrorMessage?: MakeErrorMessageFn): ResponseInterceptorConfig => {
  return {
    rejected: (error: any) => {
      console.error('errorMessageResponseInterceptor error:', error);
      if (axios.isCancel(error)) {
        return Promise.reject(error);
      }
      const err: string = error?.toString?.() ?? '';
      let errMsg = '';
      if (err?.includes('Network Error')) {
        errMsg = $t('ui.fallback.http.networkError');
      } else if (error?.message?.includes?.('timeout')) {
        errMsg = $t('ui.fallback.http.requestTimeout');
      }
      if (errMsg) {
        makeErrorMessage?.(errMsg, error);
        return Promise.reject(error);
      }
      let errorMessage = '';
      const status = error?.response?.status;
      switch (status) {
        case 400: {
          errorMessage = $t('ui.fallback.http.badRequest');
          break;
        }
        case 401: {
          errorMessage = $t('ui.fallback.http.unauthorized');
          break;
        }
        case 403: {
          errorMessage = $t('ui.fallback.http.forbidden');
          break;
        }
        case 404: {
          errorMessage = $t('ui.fallback.http.notFound');
          break;
        }
        case 408: {
          errorMessage = $t('ui.fallback.http.requestTimeout');
          break;
        }
        case 503: {
          errorMessage = $t('ui.fallback.http.serverOffline');
          break;
        }
        default: {
          errorMessage = $t('ui.fallback.http.internalServerError');
        }
      }
      makeErrorMessage?.(errorMessage, error);
      return Promise.reject(error);
    },
  };
};
