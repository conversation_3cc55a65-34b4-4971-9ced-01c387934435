<script setup lang="ts">
import { ref } from 'vue';

import { PlusOutlined } from '@ant-design/icons-vue';

import { CloudDiskFilePicker, FileList } from '#/components';

const props = defineProps({
  btnText: { type: String, default: '选择文件' },
  editMode: { type: Boolean, default: true },
  multiple: { type: Boolean, default: false },
  listType: { type: String, default: 'text' },
  filterFileType: { type: String, default: '' },
  fileInfoApi: { type: Function, default: null },
  previewExternalApi: { type: Function, default: null },
  cloudDiskApiGroup: { type: Object, default: null },
  downloadApi: { type: Function, default: null },
  uploadApi: { type: Function, default: null },
});
const emit = defineEmits(['pick']);
const CloudDiskFilePickerRef = ref();
const fileIds = defineModel<number | number[]>({ type: [Number, Array] });
const pick = async () => {
  const { id, file } = await CloudDiskFilePickerRef.value.pick({ multiple: props.multiple });
  fileIds.value = props.multiple ? [...(fileIds.value as number[]), ...(id as number[])] : (id as number);
  emit('pick', { id, file });
};
</script>

<template>
  <div :class="listType === 'picture-card' ? 'flex' : ''">
    <template v-if="editMode">
      <a-button v-if="listType === 'text'" class="mb-1" @click="pick">{{ btnText }}</a-button>
      <div
        v-else-if="listType === 'picture-card'"
        class="mr-2 flex h-24 w-24 cursor-pointer flex-col items-center justify-center border border-dashed"
        @click="pick"
      >
        <PlusOutlined />
        <div class="mt-2">{{ btnText }}</div>
      </div>
    </template>
    <FileList
      v-model="fileIds"
      :file-info-api="fileInfoApi"
      :preview-external-api="previewExternalApi"
      :download-api="downloadApi"
      :edit-mode="editMode"
      :list-type="listType"
    />
    <CloudDiskFilePicker
      ref="CloudDiskFilePickerRef"
      v-if="editMode"
      :filter-file-type="filterFileType"
      :api-suite="cloudDiskApiGroup"
      :preview-external-api="previewExternalApi"
      :download-api="downloadApi"
      :upload-api="uploadApi"
    />
  </div>
</template>

<style scoped></style>
