<script setup lang="ts">
import type { DefaultOptionType } from 'ant-design-vue/es/vc-cascader';
import type { LabelInValueType, RawValueType } from 'ant-design-vue/es/vc-select/Select';

import type { VxeGridPropTypes, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { SpuInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep } from '@vben/utils';

import { PlusOutlined } from '@ant-design/icons-vue';
import {
  AutoComplete,
  Col,
  Form,
  FormItem,
  Input,
  message,
  Modal,
  Row,
  Select,
  Space,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addSpuApi,
  editSpuApi,
  getAttributeUnitList,
  getSpecificationListApi,
  getSpuInfoApi,
  getTaxonomyListApi,
} from '#/api';
import { deleteSkuApi, disableSkuApi, editSkuApi, enableSkuApi } from '#/api/product/sku';

const emit = defineEmits(['ok']);
const colSpan = { md: 12, sm: 24 };
const init = (data: Partial<SpuInfo> = {}) => {
  goodsForm.value = {
    spuSpecList: [],
    newSkuList: [],
    skuList: [],
    spuName: '',
    spuCode: '',
    categoryId: null,
    specification: '',
    measureUnit: '',
  };
  if (data.id) {
    getDetail(data.id);
  }
};
const getDetail = async (id: number) => {
  const res = await getSpuInfoApi({ id });
  goodsForm.value = defaultsDeep(res, {
    spuSpecList: [],
    newSkuList: [],
    skuList: [],
  });
  const columns = cloneDeep(newSkuColumns);
  const spuSpecList: SpuInfo['spuSpecList'] = [];
  goodsForm.value.skuList?.forEach((specItem) => {
    if (specItem.specJson) {
      const specJsonList = JSON.parse(specItem.specJson);
      specJsonList.forEach((nItem: { specName: string; specValue: string }) => {
        specItem[nItem.specName] = nItem.specValue;
        if (!spuSpecList.some((item) => item.specName === nItem.specName)) {
          spuSpecList.push({
            specName: nItem.specName,
          });
        }
      });
    }
  });

  const targetColumn = columns?.[1];
  if (targetColumn && Array.isArray(targetColumn.children)) {
    // 1. 将确认安全的数组赋值给一个新常量
    const childrenArray = targetColumn.children;
    spuSpecList.forEach((specItem) => {
      // 2. 在循环中始终使用这个类型明确的常量
      childrenArray.push({
        field: specItem.specName,
        title: specItem.specName,
        minWidth: '160px',
      });
    });
  } else {
    console.warn('columns[1] or columns[1].children is undefined/not an array in getDetail.');
  }

  await skuGridApi.grid.loadColumn(columns);
  await skuGridApi.grid.reloadData(goodsForm.value.skuList ?? []);
};
const save = async () => {
  await formRef.value.validate();
  // const { visibleData } = newSkuGridApi.grid.getTableData();
  changeOkLoading(true);
  let api = addSpuApi;
  if (goodsForm.value.id) {
    api = editSpuApi;
  }
  const formData = cloneDeep(goodsForm.value);
  formData.skuList = [...(formData.skuList ?? []), ...(formData.newSkuList ?? [])];
  // delete formData.spuSpecList;
  // delete formData.newSkuList;
  try {
    const res = await api(formData);
    message.success('保存成功');
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const formRef = ref();
const goodsForm = ref<SpuInfo>({
  spuSpecList: [],
  newSkuList: [],
  skuList: [],
  spuName: '',
  spuCode: '',
  categoryId: 0,
  specification: '',
  measureUnit: '',
});
const rules: Record<string, any> = {
  spuCode: [{ required: true, message: '请输入商品编码', trigger: 'change' }],
  spuName: [{ required: true, message: '请输入商品名称', trigger: 'change' }],
  categoryId: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
};
const addSku = () => {
  goodsForm.value.spuSpecList?.push({ specName: '' });
};
const delSku = (index: number) => {
  if (goodsForm.value.spuSpecList) {
    goodsForm.value.spuSpecList.splice(index, 1);
  }
};
const specList = ref([]);
const getSpecList = async () => {
  const res = await getSpecificationListApi();
  specList.value = res.map((item: { specName: string; specValueJson: string }) => {
    return { label: item.specName, value: item.specName, specValueJson: item.specValueJson };
  });
};
getSpecList();
const selectSpec = (_value: any, option: any, skuItem: any) => {
  skuItem.specValueList = option.specValueJson ? JSON.parse(option.specValueJson) : [];
};
const state = reactive({
  inputVisible: false,
  inputValue: '',
});
const inputRef = ref();
const handleDeleteSpecValue = (tag: string, list: string[]) => {
  list.splice(list.indexOf(tag), 1);
};
const showInput = () => {
  state.inputVisible = true;
};

const handleInputConfirm = (skuItem: any) => {
  const inputValue = state.inputValue;
  let tags = skuItem.specValueList ?? [];
  if (inputValue && !tags.includes(inputValue)) {
    tags = [...tags, inputValue];
  }
  skuItem.specValueList = tags;
  state.inputValue = '';
  state.inputVisible = false;
};
const newSkuColumns = [
  { type: 'checkbox', width: '60px', fixed: 'left' },
  {
    field: 'spuSpecList',
    title: '商品规格',
    minWidth: '100px',
    children: [],
  },
  { field: 'skuName', title: 'SKU名称', editRender: {}, slots: { edit: 'edit_sku_name' }, minWidth: '160px' },
  { field: 'skuCode', title: 'SKU编码', editRender: {}, slots: { edit: 'edit_sku_code' }, minWidth: '160px' },
  {
    field: 'status',
    title: '状态',
    minWidth: '160px',
    formatter: ['formatBoolean', { true: '启用', false: '禁用' }],
  },
  {
    field: 'action',
    title: '操作',
    minWidth: '160px',
    fixed: 'right',
    slots: { default: 'action' },
  },
] as VxeGridPropTypes.Columns;
const newSkuGridOptions: VxeTableGridOptions = {
  showOverflow: 'title',
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  editRules: {
    bankName: [{ required: true, message: '请输入账户名称' }],
  },
  columns: newSkuColumns,
  data: goodsForm.value.newSkuList,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    // slots: {
    //   tools: 'toolbarTools',
    // },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [NewSkuGrid, newSkuGridApi] = useVbenVxeGrid({
  gridOptions: newSkuGridOptions,
});
/**
 * 处理列表数据 - 生成笛卡尔积
 */
const cartesianProduct = (specList: any[]) => {
  const result: any[] = [];
  function helper(arr: any[], index: number) {
    if (index === specList.length) {
      result.push(arr);
      return;
    }
    const currentSpec = specList[index];
    currentSpec.specValueList.forEach((value: string) => {
      helper(
        [
          ...arr,
          {
            specName: currentSpec.specName,
            specValue: value,
          },
        ],
        index + 1,
      );
    });
  }
  helper([], 0);
  return result;
};

/**
 * 计算生成的编号是否重复
 */
const calculateCode = (
  codeNum: number,
  codeName: string,
  list: string[],
  boolStr: boolean,
  callback: (num: number) => void,
) => {
  if (boolStr) {
    callback(codeNum);
  } else {
    let newCodeName: string;
    if (codeNum < 10) {
      newCodeName = `${codeName}00${codeNum}`;
    } else if (codeNum < 100) {
      newCodeName = `${codeName}0${codeNum}`;
    } else {
      newCodeName = `${codeName}${codeNum}`;
    }
    if (list.includes(newCodeName)) {
      codeNum++;
      calculateCode(codeNum, codeName, list, false, callback);
    } else {
      calculateCode(codeNum, codeName, list, true, callback);
    }
  }
};

/**
 * 生成SKU列表
 */
const createSku = () => {
  if (!goodsForm.value.spuName) {
    message.error('请填写商品名称');
    return;
  }

  // 保存当前的规格配置
  const currentSpecList = cloneDeep(goodsForm.value.spuSpecList || []);

  // 重置SKU列表
  goodsForm.value.newSkuList = [];

  // 获取有效的规格列表（有规格值的）
  const validSpecList = currentSpecList.filter(
    (item) => item.specName && item.specValueList && item.specValueList.length > 0,
  );

  if (validSpecList.length === 0) {
    if (goodsForm.value.skuList && goodsForm.value.skuList.length > 0) {
      message.error('已有商品规格，无法创建默认规格，请添加规格');
      return;
    }
    // 没有规格时，且没有已有的规格值时，创建一个默认SKU
    goodsForm.value.newSkuList.push({
      skuSpecList: [],
      skuName: goodsForm.value.spuName,
      status: 1,
      indexNum: 0,
    });
  } else {
    // 生成笛卡尔积
    const skuSpecList = cartesianProduct(validSpecList);
    let delNum = 0;

    // 去掉已有重复数据
    for (let i = 0; i < skuSpecList.length; i++) {
      // let isDuplicate = false;

      // 检查是否与已有SKU重复
      goodsForm.value.skuList?.forEach((existingSku) => {
        if (existingSku.specJson) {
          const existingSpec = JSON.parse(existingSku.specJson);
          // 移除 spuSpecId 字段以便比较
          existingSpec.forEach((item: { spuSpecId?: number }) => {
            delete item.spuSpecId;
          });

          // 将当前生成的规格转换为相同格式进行比较
          const currentSpecFormatted = skuSpecList[i].map((spec: { specName: string; specValue: string }) => ({
            specName: spec.specName,
            specValue: spec.specValue,
          }));

          // 比较规格组合是否相同
          const existingSpecJson = JSON.stringify(existingSpec).replaceAll(/\s/g, '');
          const currentSpecJson = JSON.stringify(currentSpecFormatted).replaceAll(/\s/g, '');

          if (existingSpecJson === currentSpecJson) {
            // isDuplicate = true;
            delNum++;
            skuSpecList.splice(i, 1);
            // i--;
          }
        }
      });

      // if (isDuplicate) break;
    }

    // 显示重复检测结果
    if (delNum !== 0) {
      message.warning(`检测到 ${delNum} 个已存在的SKU，生成时已自动跳过。`);
    }

    // 为每个SKU组合创建SKU项
    skuSpecList.forEach((item, index) => {
      if (!goodsForm.value.newSkuList) {
        goodsForm.value.newSkuList = [];
      }
      goodsForm.value.newSkuList.push({
        skuSpecList: item,
        status: 1,
        indexNum: index,
      });
    });
  }

  // 获取已有编码
  const hasSkuCodeList: string[] = [];
  goodsForm.value.skuList?.forEach((item) => {
    if (item.skuCode) {
      hasSkuCodeList.push(item.skuCode);
    }
  });

  // 设置SKU名称和规格值
  goodsForm.value.newSkuList?.forEach((item: any) => {
    const newSkuName: string[] = [];
    item.skuSpecList?.forEach((cItem: any) => {
      item[cItem.specName] = cItem.specValue;
      newSkuName.push(cItem.specValue);
    });

    // 默认名称：多个规格中间加空格
    if (!item.skuName) {
      item.skuName = newSkuName.length > 0 ? newSkuName.join(' ') : goodsForm.value.spuName;
    }
  });

  // 生成SKU编码
  let newSkuCode = 0;
  for (let i = 0; i < (goodsForm.value.newSkuList?.length || 0); i++) {
    newSkuCode++;
    const spuCode = goodsForm.value.spuCode || '';
    calculateCode(newSkuCode, spuCode, hasSkuCodeList, false, (result) => {
      const item = goodsForm.value.newSkuList?.[i];
      if (item && !item.skuCode) {
        newSkuCode = result;
        if (newSkuCode < 10) {
          item.skuCode = `${spuCode}00${newSkuCode}`;
        } else if (newSkuCode < 100) {
          item.skuCode = `${spuCode}0${newSkuCode}`;
        } else {
          item.skuCode = `${spuCode}${newSkuCode}`;
        }
      }
    });
  }

  message.success(`成功生成 ${goodsForm.value.newSkuList?.length || 0} 个SKU`);
  const columns = cloneDeep(newSkuColumns) as VxeGridPropTypes.Columns;

  const targetColumn = columns?.[1];
  if (targetColumn && Array.isArray(targetColumn.children)) {
    // 1. 将确认安全的数组赋值给一个新常量
    const childrenArray = targetColumn.children;

    goodsForm.value.spuSpecList?.forEach((specItem) => {
      if (specItem.specName) {
        // 2. 在循环中始终使用这个类型明确的常量
        childrenArray.push({
          field: specItem.specName,
          title: specItem.specName,
          minWidth: '160px',
        } as VxeGridPropTypes.Column);
      }
    });
  } else {
    console.warn('columns[1] or columns[1].children is undefined/not an array in createSku.');
  }

  newSkuGridApi.grid.loadColumn(columns);
  newSkuGridApi.grid.reloadData(goodsForm.value.newSkuList || []);
};
const batchDelNewSku = () => {
  const res = newSkuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  goodsForm.value.newSkuList = goodsForm.value.newSkuList?.filter((item) => !res.includes(item)) || [];
  newSkuGridApi.grid.reloadData(goodsForm.value.newSkuList);
};
const batchChangeStatusNewSku = (status: number) => {
  const res = newSkuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  res.forEach((item: any) => {
    item.status = status;
  });
  newSkuGridApi.grid.reloadData(goodsForm.value.newSkuList || []);
};
const [SkuGrid, skuGridApi] = useVbenVxeGrid({
  gridOptions: newSkuGridOptions,
  gridEvents: {
    editClosed: ({ row }: { row: any }) => {
      skuEdit(row);
    },
  },
});
const skuEdit = async (row: any) => {
  await editSkuApi(row);
  message.success('保存成功');
};
const changeSkuStatus = (row: any, status: 0 | 1) => {
  const pidList = [row.id];
  const operation = {
    1: {
      label: '启用',
      api: enableSkuApi,
    },
    0: {
      label: '禁用',
      api: disableSkuApi,
    },
  };
  Modal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此SKU吗？`,
    async onOk() {
      await operation[status].api(pidList);
      message.success('操作成功');
      row.status = status;
      // await skuGridApi.reload();
    },
  });
};
const batchDelSku = async () => {
  const res = skuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList: number[] = [];
  res.forEach((item: any) => {
    pidList.push(item.id);
  });
  await deleteSkuApi(pidList);
  message.success('操作成功');
  // 远程删除成功后，同步删除本地数据
  goodsForm.value.skuList = goodsForm.value.skuList?.filter((item) => !pidList.includes(item.id as number)) || [];
  // 刷新表格数据
  await skuGridApi.grid.reloadData(goodsForm.value.skuList);
};
const batchChangeStatusSku = (status: 0 | 1) => {
  const res = skuGridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList: number[] = [];
  res.forEach((item: any) => {
    pidList.push(item.id);
  });
  const operation = {
    1: {
      label: '启用',
      api: enableSkuApi,
    },
    0: {
      label: '禁用',
      api: disableSkuApi,
    },
  };
  Modal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此SKU吗？`,
    async onOk() {
      await operation[status].api(pidList);
      message.success('操作成功');
      res.forEach((item: any) => {
        item.status = status;
      });
      await skuGridApi.grid.reloadData(goodsForm.value.skuList || []);
      // await skuGridApi.reload();
    },
  });
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="商品信息" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="goodsForm"
      :rules="rules"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="商品编码" name="spuCode">
            <Input v-model:value="goodsForm.spuCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="商品名称" name="spuName">
            <Input v-model:value="goodsForm.spuName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="商品分类" name="categoryId">
            <ApiComponent
              v-model="goodsForm.categoryId as unknown as string"
              :component="Select"
              :api="getTaxonomyListApi"
              label-field="categoryName"
              value-field="id"
              model-prop-name="value"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="规格型号" name="specification">
            <Input v-model:value="goodsForm.specification" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="计量单位" name="measureUnit">
            <ApiComponent
              v-model="goodsForm.measureUnit"
              :component="Select"
              :api="getAttributeUnitList"
              label-field="unitName"
              value-field="unitName"
              model-prop-name="value"
            />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="商品规格" />
      <Row class="mt-5">
        <Col :span="24">
          <a-card v-for="(item, index) in goodsForm.spuSpecList" :key="index" class="mb-4">
            <template #title>
              <div class="w-[400px]">
                <FormItem label="规格名" class="mb-0">
                  <AutoComplete
                    v-model:value="item.specName"
                    :options="specList"
                    @select="
                      (value: RawValueType | LabelInValueType, option: DefaultOptionType) =>
                        selectSpec(value, option, item)
                    "
                  />
                </FormItem>
              </div>
            </template>
            <template #extra>
              <a-button type="link" @click="delSku(index)">删除规格</a-button>
            </template>
            <a-tag
              v-for="(tag, tagIndex) in item.specValueList"
              :key="tag + tagIndex"
              closable
              color="blue"
              @close="handleDeleteSpecValue(tag, item.specValueList ?? [])"
            >
              {{ tag }}
            </a-tag>
            <template v-if="item">
              <a-input
                v-if="state.inputVisible"
                ref="inputRef"
                v-model:value="state.inputValue"
                type="text"
                size="small"
                :style="{ width: '78px' }"
                @blur="handleInputConfirm(item)"
                @keyup.enter="handleInputConfirm(item)"
              />
              <a-tag v-else class="cursor-pointer" @click="showInput">
                <PlusOutlined />
                添加
              </a-tag>
            </template>
          </a-card>
        </Col>
        <Col :span="24">
          <div class="mt-2">
            <a-button type="primary" @click="addSku">添加规格</a-button>
          </div>
          <div class="mt-2">
            <Space>
              <a-button type="primary" @click="createSku">生成SKU</a-button>
              <a-button type="primary" danger @click="batchDelNewSku">批量删除</a-button>
              <a-button type="primary" @click="batchChangeStatusNewSku(1)">启用</a-button>
              <a-button type="primary" @click="batchChangeStatusNewSku(0)">禁用</a-button>
            </Space>
          </div>
        </Col>
        <Col :span="24">
          <NewSkuGrid>
            <template #edit_sku_name="{ row }">
              <Input v-model:value="row.skuName" placeholder="请输入SKU名称" />
            </template>
            <template #edit_sku_code="{ row }">
              <Input v-model:value="row.skuCode" placeholder="请输入SKU编码" />
            </template>
            <template #action="{ row }">
              <Space>
                <TypographyLink v-if="row.status === 0" @click="row.status = 1">启用</TypographyLink>
                <TypographyLink v-else type="danger" @click="row.status = 0">禁用</TypographyLink>
              </Space>
            </template>
          </NewSkuGrid>
        </Col>
      </Row>
      <BasicCaption content="已有商品规格" />
      <Row class="mt-5">
        <Col :span="24">
          <Space>
            <a-button type="primary" danger @click="batchDelSku">批量删除</a-button>
            <a-button type="primary" @click="batchChangeStatusSku(1)">启用</a-button>
            <a-button type="primary" @click="batchChangeStatusSku(0)">禁用</a-button>
          </Space>
        </Col>
        <Col :span="24">
          <SkuGrid>
            <template #edit_sku_name="{ row }">
              <Input v-model:value="row.skuName" placeholder="请输入SKU名称" />
            </template>
            <template #edit_sku_code="{ row }">
              <Input v-model:value="row.skuCode" placeholder="请输入SKU编码" />
            </template>
            <template #action="{ row }">
              <Space>
                <TypographyLink v-if="row.status === 0" @click="changeSkuStatus(row, 1)">启用</TypographyLink>
                <TypographyLink v-else type="danger" @click="changeSkuStatus(row, 0)">禁用</TypographyLink>
              </Space>
            </template>
          </SkuGrid>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
