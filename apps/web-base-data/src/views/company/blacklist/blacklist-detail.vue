<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { BlackListAddInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP, DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { formatDateTime } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getBlackListDetailApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const state = reactive({
  pageType: 'detail',
});
const {
  initWorkflow,
  isWorkflowLoading,
  isWorkflow,
  isProcessInstance,
  isRunningTask,
  useOperationButton,
  useViewButton,
} = useWorkflowBase();
const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const init = async (data: BlackListAddInfo & { pageType?: string }) => {
  state.pageType = data.pageType ?? 'detail';
  blacklistForm.value = data.id
    ? await getBlackListDetailApi({ id: data.id })
    : {
        ...data,
      };
  await gridApi.grid.reloadData(blacklistForm.value.operationLogs ?? []);
  await initWorkflow({ formKey: 'company_blacklist', businessKey: data.id });
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const blacklistForm = ref<BlackListAddInfo>({
  companyName: '',
  companyCode: '',
  remark: '',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'operationType', title: '操作类型' },
    { field: 'operatorName', title: '操作人' },
    { field: 'operationTime', title: '操作时间', formatter: 'formatDate' },
    { field: 'remark', title: '原因' },
  ],
  ...DETAIL_GRID_OPTIONS,
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const workflowSuccess = () => {
  closePopup();
  emit('ok');
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="黑名单信息" @register="registerPopup">
    <template #insertToolbar>
      <div v-loaind="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance && state.pageType === 'audit'" @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow && !isRunningTask && state.pageType === 'detail'" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
        <a-descriptions-item label="企业名称">
          {{ blacklistForm.companyName }}
        </a-descriptions-item>
        <a-descriptions-item label="统一社会信用代码">
          {{ blacklistForm.companyCode }}
        </a-descriptions-item>
        <a-descriptions-item label="添加时间">
          {{ formatDateTime(blacklistForm.addTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="移除时间">
          {{ formatDateTime(blacklistForm.removeTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="其他备注" :span="2">
          {{ blacklistForm.remark }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="操作记录" />
      <Grid />
    </div>
  </BasicPopup>
</template>

<style></style>
