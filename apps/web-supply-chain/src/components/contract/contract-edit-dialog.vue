<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ContractInfo } from '#/api';

import { nextTick, ref } from 'vue';

import { OnlyOffice } from '@vben/base-ui';
import { ApiComponent, useVbenModal } from '@vben/common-ui';
import { COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { getCombinedErrorMessagesString } from '@vben/utils';

import { AutoComplete, message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { BaseFilePickList } from '#/adapter/base-ui';
import {
  copyFileApi,
  getContractClassifyListApi,
  getContractSignInfoApi,
  getContractTemplateInfoApi,
  getOnlyOfficeFileInfoApi,
  getTempContractInfoApi,
  saveBusinessContractSignApi,
  saveOnlyOfficeFileApi,
  saveTempContractSignApi,
} from '#/api';

const emit = defineEmits(['success']);
const [Modal, modalApi] = useVbenModal({
  onConfirm: () => {
    save();
  },
});
const dictStore = useDictStore();
const tempFlag = ref(false);
const init = async (data: ContractInfo, isTemp: boolean = false) => {
  tempFlag.value = isTemp;
  if (data.id) {
    const detailApi = tempFlag.value ? getTempContractInfoApi : getContractSignInfoApi;
    contractForm.value = await detailApi({ id: data.id });
  } else {
    contractForm.value = { ...data };
    if (contractForm.value.createType === '2' && contractForm.value.templateId) {
      const templateInfo = await getContractTemplateInfoApi({ id: contractForm.value.templateId });
      const res = await copyFileApi({ id: templateInfo.fileId });
      contractForm.value.fileId = res.id;
    }
  }
  modalApi.open();
  await nextTick();
  await gridApi.grid.reloadData(contractForm.value.signDetailList ?? []);
  if (contractForm.value.createType === '2') {
    EditorRef.value.init(contractForm.value.fileId, 'edit');
  }
};
const modalTitle = ref('编辑合同');
const ContractFormRef = ref();
const EditorRef = ref();
const contractForm = ref<ContractInfo>({});
const rules = {
  contractName: [{ required: true, message: '请输入合同名称' }],
  // contractCode: [{ required: true, message: '请输入合同编码' }],
  signMethod: [{ required: true, message: '请选择签约方式', trigger: 'change' }],
};
const colSpanProp = COL_SPAN_PROP;
const save = async () => {
  await ContractFormRef.value.validate();
  const errMap = await gridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
      return;
    }
  }
  try {
    const formData = cloneDeep(contractForm.value);
    const { visibleData } = gridApi.grid.getTableData();
    formData.signDetailList = visibleData;
    modalApi.lock();
    const saveApi = tempFlag.value ? saveTempContractSignApi : saveBusinessContractSignApi;
    const res = await saveApi(formData);
    emit('success', res);
    message.success('保存成功');
    await modalApi.close();
  } finally {
    modalApi.unlock();
  }
};
const baseGridOptions = {
  showOverflow: 'title',
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const signGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'signerType',
      title: '类型',
      minWidth: '100px',
      formatter: ['formatStatus', 'SIGN_USER_TYPE'],
    },
    {
      field: 'signatoryParam',
      title: '签约角色',
      slots: { default: 'edit_signatory_param' },
      minWidth: '160px',
    },
    {
      field: 'signInfo',
      title: '签约方信息',
      slots: { default: 'edit_sign_info' },
      minWidth: '500px',
    },
  ],
  editRules: {
    signatoryParam: [{ required: true, content: '请输入签约角色' }],
    signInfo: [
      {
        validator({ row }) {
          if (row.signerType === '1' && !row.signatoryOrg) {
            return new Error('请输入签约企业');
          }
          if (row.signerType === '0' && !row.signatoryName) {
            return new Error('请输入真实姓名');
          }
          // if (!row.signatoryAccount) {
          //   return new Error('请输入手机号或邮箱');
          // }
          return true;
        },
      },
    ],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: signGridOptions,
});
const addSignatory = (type: string) => {
  gridApi.grid.insertAt({
    signerType: type,
  });
};
const removeSignatory = () => {
  const records = gridApi.grid.getCheckboxRecords();
  gridApi.grid.remove(records);
};
const saveDoc = async () => {
  await EditorRef.value.save();
};
defineExpose({
  init,
  modalApi,
});
</script>

<template>
  <Modal class="w-[1200px]" :title="modalTitle">
    <div class="">
      <a-form ref="ContractFormRef" class="" :model="contractForm" :rules="rules" v-bind="FORM_PROP">
        <a-row class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同名称" name="contractName">
              <a-input v-model:value="contractForm.contractName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同编码" name="contractCode">
              <a-input v-model:value="contractForm.contractCode" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="用章类型" name="sealType">
              <a-select v-model:value="contractForm.sealType" :options="dictStore.getDictList('SEAL_TYPE')" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同分类" name="categoryId">
              <ApiComponent
                :component="Select"
                v-model="contractForm.categoryId as unknown as string"
                :api="getContractClassifyListApi"
                label-field="categoryName"
                value-field="id"
                model-prop-name="value"
              />
            </a-form-item>
          </a-col>
          <!--<a-col v-bind="colSpanProp">-->
          <!--  <a-form-item label="签约类型" name="signMethod">-->
          <!--    <a-select v-model:value="contractForm.signMethod" :options="dictStore.getDictList('baseEnableType')" />-->
          <!--  </a-form-item>-->
          <!--</a-col>-->
        </a-row>
        <BasicCaption content="设置签约方" />
        <Grid>
          <template #toolbarTools>
            <a-space>
              <a-button type="primary" @click="addSignatory('0')">添加签约个人</a-button>
              <a-button type="primary" @click="addSignatory('1')">添加签约企业</a-button>
              <a-button type="primary" danger @click="removeSignatory">删除</a-button>
            </a-space>
          </template>
          <template #edit_signatory_param="{ row }">
            <AutoComplete v-model:value="row.signatoryParam" class="w-full" />
          </template>
          <template #edit_sign_info="{ row }">
            <a-space>
              <a-input v-if="row.signerType === '1'" v-model:value="row.signatoryOrg" placeholder="请输入签约企业" />
              <a-input v-model:value="row.signatoryName" placeholder="请输入真实姓名" />
              <a-input v-model:value="row.signatoryAccount" placeholder="请输入手机号或邮箱" />
            </a-space>
          </template>
          <template #sign_info="{ row }">
            <a-space>
              <span v-if="row.signerType === '1'">{{ row.signatoryOrg }}</span>
              <span>{{ row.signatoryName }}</span>
              <span>{{ row.signatoryAccount }}</span>
            </a-space>
          </template>
        </Grid>
        <BasicCaption content="合同文件">
          <template v-if="contractForm.createType === '2'" #action>
            <a-button type="primary" @click="saveDoc">保存</a-button>
          </template>
        </BasicCaption>
        <a-row v-if="contractForm.createType === '1'" class="mt-5">
          <a-col v-bind="colSpanProp">
            <a-form-item label="合同（源文件）" name="fileId">
              <BaseFilePickList v-model="contractForm.fileId" />
            </a-form-item>
          </a-col>
        </a-row>
        <template v-else-if="contractForm.createType === '2'">
          <div class="h-[600px]">
            <OnlyOffice ref="EditorRef" :get-config-api="getOnlyOfficeFileInfoApi" :save-api="saveOnlyOfficeFileApi" />
          </div>
        </template>
      </a-form>
    </div>
  </Modal>
</template>

<style></style>
