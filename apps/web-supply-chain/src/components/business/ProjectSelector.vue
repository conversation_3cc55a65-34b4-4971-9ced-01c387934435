<script setup>
import { ModelSelect } from '@vben/base-ui';
import { useDictStore } from '@vben/stores';

import { getProjectListApi } from '#/api';

const modelValue = defineModel();

const dictStore = useDictStore();

const columns = [
  { title: '项目名称', dataIndex: 'projectName', key: 'projectName' },
  { title: '项目编号', dataIndex: 'projectCode', key: 'projectCode' },
  {
    title: '业务结构',
    dataIndex: 'businessStructure',
    key: 'businessStructure',
    customRender: ({ value }) => dictStore.formatter(value, 'BUS_STRUCTURE'),
  },
  {
    title: '项目模式',
    dataIndex: 'projectModel',
    key: 'projectModel',
    customRender: ({ value }) => dictStore.formatter(value, 'PROJECT_MODE'),
  },
];
</script>

<template>
  <ModelSelect
    v-model="modelValue"
    :columns="columns"
    :source-api="getProjectListApi"
    label-key="projectName"
    value-key="id"
    :search-keys="['projectName', 'projectCode']"
    title="选择项目"
    search-placeholder="请输入项目名称或编号"
    v-bind="$attrs"
  />
</template>
