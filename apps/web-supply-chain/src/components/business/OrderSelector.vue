<script setup>
import { computed } from 'vue';

import { ModelSelect } from '@vben/base-ui';
import { useDictStore } from '@vben/stores';

import { getPurchaseOrderListApi, getSalesOrderListApi } from '#/api';

const props = defineProps({
  orderType: {
    type: String,
    required: true,
    validator: (value) => ['purchase', 'sales'].includes(value),
  },
  projectId: {
    type: [String, Number],
    default: undefined,
  },
  status: {
    type: String,
    default: 'EFFECTIVE',
  },
});

const emit = defineEmits(['change']);
const modelValue = defineModel();
const dictStore = useDictStore();

// 处理选择事件
const handleSelect = (value, option) => {
  emit('change', value, option);
};

// 使用关联订单API
const sourceApi = computed(() => {
  if (props.orderType === 'sales') {
    return getSalesOrderListApi;
  }
  return getPurchaseOrderListApi;
});

// 根据订单类型设置不同的列配置
const columns = computed(() => {
  const baseColumns = [
    {
      title: '项目名称',
      dataIndex: 'projectName',
      key: 'projectName',
    },
    {
      title: '业务结构',
      dataIndex: 'businessStructure',
      key: 'businessStructure',
      customRender: ({ value }) => dictStore.formatter(value, 'BUS_STRUCTURE'),
    },
    {
      title: '项目模式',
      dataIndex: 'projectModel',
      key: 'projectModel',
      customRender: ({ value }) => dictStore.formatter(value, 'PROJECT_MODE'),
    },
  ];

  // 根据订单类型添加特定列
  if (props.orderType === 'sales') {
    baseColumns.unshift(
      {
        title: '订单编号',
        dataIndex: 'salesOrderCode',
        key: 'salesOrderCode',
      },
      {
        title: '订单名称',
        dataIndex: 'salesOrderName',
        key: 'salesOrderName',
      },
    );
    baseColumns.push({
      title: '下游企业',
      dataIndex: 'purchaserCompanyName',
      key: 'purchaserCompanyName',
    });
  } else {
    baseColumns.unshift(
      {
        title: '订单编号',
        dataIndex: 'purchaseOrderCode',
        key: 'purchaseOrderCode',
      },
      {
        title: '订单名称',
        dataIndex: 'purchaseOrderName',
        key: 'purchaseOrderName',
      },
    );
    baseColumns.push({
      title: '上游企业',
      dataIndex: 'supplierCompanyName',
      key: 'supplierCompanyName',
    });
  }

  return baseColumns;
});

// 根据订单类型设置不同的标题和占位符
const title = computed(() => {
  return props.orderType === 'sales' ? '选择销售订单' : '选择采购订单';
});

const searchPlaceholder = computed(() => {
  return props.orderType === 'sales' ? '请输入销售订单名称或编号' : '请输入采购订单名称或编号';
});

// 搜索字段
const searchKeys = ['orderName', 'orderCode'];

// 标签字段
const labelKey = computed(() => {
  return props.orderType === 'sales' ? 'salesOrderName' : 'purchaseOrderName';
});

// API参数
const apiParams = computed(() => {
  const orderTypeMap = {
    sales: 'SALES_ORDER',
    purchase: 'PURCHASE_ORDER',
  };

  const params = {
    orderType: orderTypeMap[props.orderType],
    status: props.status,
  };

  if (props.projectId) {
    params.projectId = props.projectId;
  }

  return params;
});
</script>

<template>
  <ModelSelect
    v-model="modelValue"
    :columns="columns"
    :source-api="sourceApi"
    :label-key="labelKey"
    value-key="id"
    :search-keys="searchKeys"
    :title="title"
    :search-placeholder="searchPlaceholder"
    :source-query="apiParams"
    @change="handleSelect"
    v-bind="$attrs"
  />
</template>
