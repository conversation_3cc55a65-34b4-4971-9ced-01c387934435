import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '结算管理',
      order: 8,
    },
    name: 'settle-manage',
    path: '/settle-manage',
    children: [
      {
        name: 'purchaseSettle',
        path: 'purchase-settle',
        component: () => import('#/views/settle-manage/purchase/index.vue'),
        meta: {
          title: '采购结算单',
        },
      },
      // {
      //   name: 'saleSettle',
      //   path: 'sale-settle',
      //   component: () => import('#/views/settle-manage/sale/index.vue'),
      //   meta: {
      //     title: '销售结算单',
      //   },
      // },
      // {
      //   name: 'collaborationCostsSettle',
      //   path: 'collaboration-costs-settle',
      //   component: () => import('#/views/settle-manage/collaborationCosts/index.vue'),
      //   meta: {
      //     title: '合作费用结算单',
      //   },
      // },
    ],
  },
];

export default routes;
