<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ProjectBaseInfo, ProjectPartners } from '#/api';

import { computed, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatDate } from '@vben/utils';

import { Descriptions, DescriptionsItem, message, TabPane, Tabs } from 'ant-design-vue';

import { BaseArchiveList, BaseAttachmentList, BaseFileList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  getCompanyApi,
  getProjectReviewLogApi,
  projectBranchDetailApi,
  projectChangeDetailApi,
  projectGeneralDetailApi,
  projectManageDetailApi,
  projectProposalDetailApi,
  projectReviewDetailApi,
} from '#/api';
import { ContractList } from '#/components';
import { getDictLabel, getMultiDictLabels } from '#/utils/data-process';

defineEmits(['register', 'ok']);
const { getDictList } = useDictStore();

// 根据接口定义初始化产品信息
const defaultForm = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  projectCode: Date.now().toString(),
  projectName: undefined,
  executorCompanyCode: undefined,
  executorCompanyName: '江西财投集团有限责任公司',
  businessStructure: undefined,
  projectModel: undefined,
  purchaseMode: [],
  isGoodsControlMode: undefined,
  paymentTermDays: undefined,
  planStartDate: undefined,
  creditDueDate: undefined,
  creditAmount: undefined,
  serviceFeeRate: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  remarks: undefined,
  isDeposit: undefined,
  mortgageInfoDesc: undefined,
  pledgeInfoDesc: undefined,
  mortgageInfoAttachment: undefined,
  pledgeInfoAttachment: undefined,
  guaranteeInfoDesc: undefined,
  riskControlDesc: undefined,
  creditEnhancementDesc: undefined,
  status: undefined,
  approvalStatus: undefined,
  paymentMethod: [],
  collectionMethod: [],
  settlementMethod: undefined,
  isKeyIndustry: undefined,
  isRealEnterprise: undefined,
  businessManagerId: [],
  operationManagerId: [],
  financeManagerId: [],
  riskManagerId: [],
  guaranteeCompanyCode: undefined,
  guaranteeCompanyName: undefined,
  creditType: undefined,
  projectReviewId: undefined,
  partyBranchId: undefined,
  generalManagerId: undefined,
  reviewNodeId: undefined,
  businessManager: [],
  operationManager: [],
  financeManager: [],
  riskManager: [],
  attachmentList: [],
  projectPartners: [
    {
      id: undefined,
      version: undefined,
      projectId: undefined,
      partnerType: undefined,
      companyCode: undefined,
      companyName: undefined,
      subLimitAmount: undefined,
      occupyLimit: undefined,
      creditType: undefined,
      expiryDate: undefined,
    },
  ],
};

const detailForm = reactive<Partial<ProjectBaseInfo & { contractList: any[] }>>(cloneDeep(defaultForm));
const pageLoading = ref(false);

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

// 解析逗号分隔字符串为数组的函数
const parseCommaSeparatedField = (fieldValue: any): string[] => {
  if (typeof fieldValue === 'string' && fieldValue) {
    return fieldValue.split(',').filter((item) => item !== '');
  } else if (Array.isArray(fieldValue)) {
    return fieldValue;
  }
  return [];
};

const currentProjectType = ref('');

const init = async (data: ProjectBaseInfo) => {
  pageLoading.value = true;
  await getCompanyList();
  if (data.id) {
    currentProjectType.value = data.projectType;
    const apiMap = {
      review: projectReviewDetailApi,
      branch: projectBranchDetailApi,
      general: projectGeneralDetailApi,
      initiation: projectProposalDetailApi,
      info: projectManageDetailApi,
      change: projectChangeDetailApi,
    } as const;

    const api = (data.projectType && apiMap[data.projectType as keyof typeof apiMap]) || projectReviewDetailApi;

    try {
      const nodeBasedTypes: (string | undefined)[] = ['review', 'branch', 'general'];
      const useReviewNodeId = nodeBasedTypes.includes(data.projectType);
      const queryId = useReviewNodeId && data.reviewNodeId ? data.reviewNodeId : data.id;

      const res: Partial<ProjectBaseInfo> = await api(queryId);

      Object.assign(detailForm, res);

      detailForm.purchaseMode = parseCommaSeparatedField(detailForm.purchaseMode);
      detailForm.paymentMethod = parseCommaSeparatedField(detailForm.paymentMethod);
      detailForm.collectionMethod = parseCommaSeparatedField(detailForm.collectionMethod);

      // 强制校验并转换projectPartners字段
      detailForm.projectPartners =
        !Array.isArray(res.projectPartners) || res.projectPartners === null ? [] : [...res.projectPartners];

      // 强制刷新表格
      if (gridApiSupplier?.grid) {
        await gridApiSupplier.grid.reloadData(
          detailForm.projectPartners.filter((item) => item.partnerType === 'SUPPLIER'),
        );
      }
      if (gridApiPurchaser?.grid) {
        await gridApiPurchaser.grid.reloadData(
          detailForm.projectPartners.filter((item) => item.partnerType === 'PURCHASER'),
        );
      }
      if (gridApiCredit?.grid) {
        await gridApiCredit.grid.reloadData(
          detailForm.projectPartners.filter((item) => item.partnerType === 'CREDIT_COMPANY'),
        );
      }
      const history = await getProjectReviewLogApi({ projectId: data.id, projectType: 'PROJECT_CHANGE' });
      await historyGridApi.grid.reloadData(history);
    } catch (error) {
      console.error('获取项目详情失败:', error);
      message.error('获取项目详情失败');
    }
  }
  pageLoading.value = false;
};
const activeKey = ref('info');
const titleMap = {
  review: '项目评审详情',
  branch: '支委会申请详情',
  general: '总经办申请详情',
  info: '项目总览',
  initiation: '项目立项详情',
} as const;

const title = computed(() => {
  return titleMap[currentProjectType.value as keyof typeof titleMap] || '';
});

const [registerPopup] = usePopupInner(init);

const accountList = ref<ProjectPartners[]>([]);

// 新增初始化方法
const setAccountData = (data: ProjectPartners[]) => {
  accountList.value = data;
  // 更新所有相关表格
  if (gridApiSupplier.grid) {
    gridApiSupplier.grid.reloadData(accountList.value);
  }
  if (gridApiPurchaser.grid) {
    gridApiPurchaser.grid.reloadData(accountList.value);
  }
  if (gridApiCredit.grid) {
    gridApiCredit.grid.reloadData(accountList.value);
  }
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: 'title',
  keepSource: true,
  border: 'inner',
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridOptionsSupplier: VxeTableGridOptions = {
  ...gridOptions,
  columns: [
    {
      field: 'companyName',
      title: '企业名称',
      slots: { default: 'companyName' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { default: 'companyCode' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsPurchaser: VxeTableGridOptions = {
  ...gridOptions,
  columns: [
    {
      field: 'companyName',
      title: '企业名称',
      slots: { default: 'companyName' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { default: 'companyCode' },
      minWidth: '160px',
    },
  ],
  data: [],
};

const gridOptionsCredit: VxeTableGridOptions = {
  ...gridOptions,
  columns: [
    {
      field: 'companyName',
      title: '企业名称',
      slots: { default: 'companyName' },
      minWidth: '160px',
    },
    {
      field: 'companyCode',
      title: '统一社会信用代码',
      slots: { default: 'companyCode' },
      minWidth: '160px',
    },
    {
      field: 'subLimitAmount',
      title: '企业额度上限（元）',
      slots: { default: 'subLimitAmount' },
      minWidth: '160px',
    },
    {
      field: 'occupyLimit',
      title: '是否占用企业总额度',
      slots: { default: 'occupyLimit' },
      formatter: ['formatStatus', 'baseBooleanType'],
      minWidth: '160px',
    },
  ],
  data: [],
};

const [GridSupplier, gridApiSupplier] = useVbenVxeGrid({
  gridOptions: gridOptionsSupplier,
  // tableTitle: '上游企业',
});
const [GridPurchaser, gridApiPurchaser] = useVbenVxeGrid({
  gridOptions: gridOptionsPurchaser,
  // tableTitle: '下游企业',
});
const [GridCredit, gridApiCredit] = useVbenVxeGrid({
  gridOptions: gridOptionsCredit,
  // tableTitle: '终端企业',
});
const [HistoryGrid, historyGridApi] = useVbenVxeGrid({
  gridOptions: {
    ...gridOptions,
    columns: [
      {
        field: 'createTime',
        title: '发起评审时间',
        minWidth: '160px',
        formatter: 'formatDate',
      },
      {
        field: 'projectReviewStatus',
        title: '项目评审状态',
        minWidth: '160px',
        slots: { default: 'reviewStatus' },
      },
      {
        field: 'partyBranchMeetingStatus',
        title: '党支部会议状态',
        minWidth: '160px',
        slots: { default: 'reviewStatus' },
      },
      {
        field: 'generalManagerOfficeStatus',
        title: '总经理办公会状态',
        minWidth: '160px',
        slots: { default: 'reviewStatus' },
      },
      {
        field: 'reviewType',
        title: '评审类型',
        minWidth: '160px',
        cellRender: {
          name: 'CellStatus',
          props: {
            code: 'PROJECT_REVIEW_TYPE',
          },
        },
      },
    ],
    data: [],
  },
});

const filteredProjectModeOptions = ref<any[]>([]);

// 处理多名负责人名称显示
const getManagerNames = (managers: any[] | undefined) => {
  if (!managers || !Array.isArray(managers) || managers.length === 0) {
    return '';
  }
  return managers
    .map((manager) => manager.userName)
    .filter(Boolean)
    .join(', ');
};

// 处理企业code转换为名称显示
const getCompanyLabel = (companyList: any[], code: string | undefined) => {
  if (!code) return '';
  const item = companyList.find((item) => item.companyCode === code);
  return item ? item.companyName : code;
};

// 只监听真正需要的依赖
watch(
  [() => detailForm.id, () => detailForm.projectModel, () => detailForm.businessStructure],
  ([id, projectModel, businessStructure]) => {
    const options = getDictList('PROJECT_MODE');

    // 编辑模式且已有项目模式值时，返回所有选项
    if (id && projectModel) {
      filteredProjectModeOptions.value = options;
      return;
    }

    // 根据业务结构过滤选项
    filteredProjectModeOptions.value = options.map((option) => {
      if (businessStructure === 'PURCHASE' && (option.value === 'BUILDING' || option.value === 'WAREHOUSE_SUPPLY')) {
        return { ...option, disabled: true };
      }
      return { ...option, disabled: false };
    });
  },
  { immediate: true }, // 立即执行一次
);
const router = useRouter();
const viewDetail = (row: any, field: string) => {
  const pageInfoList = {
    projectReviewStatus: {
      pageName: 'Review',
      idKey: 'projectReviewId',
    },
    partyBranchMeetingStatus: {
      pageName: 'BranchCommittee',
      idKey: 'partyBranchId',
    },
    generalManagerOfficeStatus: {
      pageName: 'GeneralManager',
      idKey: 'generalManagerId',
    },
  };
  const pageInfo = pageInfoList[field];
  const query = {
    id: row[pageInfo.idKey],
    modal: 'detail',
  };
  router.push({
    name: pageInfo.pageName,
    query,
  });
};

// 暴露方法给父组件
defineExpose({
  getAccountData() {
    // 合并所有表格的数据
    let allData: any[] = [];

    if (gridApiSupplier.grid) {
      const { tableData } = gridApiSupplier.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    if (gridApiPurchaser.grid) {
      const { tableData } = gridApiPurchaser.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    if (gridApiCredit.grid) {
      const { tableData } = gridApiCredit.grid.getTableData();
      allData = [...allData, ...tableData];
    }

    return allData;
  },
  setAccountData,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" :loading="pageLoading" destroy-on-close @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Tabs v-model:active-key="activeKey">
        <TabPane key="info" tab="项目信息">
          <div>
            <Descriptions v-bind="DESCRIPTIONS_PROP">
              <!-- 基本信息 -->
              <DescriptionsItem label="项目编号">
                <span v-if="detailForm.projectCode">
                  {{ detailForm.projectCode }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="项目名称">
                <span v-if="detailForm.projectName">
                  {{ detailForm.projectName }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="业务结构">
                <span v-if="detailForm.businessStructure">
                  {{ getDictLabel(getDictList('BUS_STRUCTURE'), detailForm.businessStructure) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="项目模式">
                <span v-if="detailForm.projectModel">
                  {{ getDictLabel(getDictList('PROJECT_MODE'), detailForm.projectModel) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="贸易执行企业">
                <span v-if="detailForm.executorCompanyName">
                  {{ detailForm.executorCompanyName }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="担保企业">
                <span v-if="detailForm.guaranteeCompanyCode">
                  {{ getCompanyLabel(companyOptions, detailForm.guaranteeCompanyCode) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="是否有保证金">
                <span v-if="detailForm.isDeposit != null">
                  {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isDeposit) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="是否是控货模式">
                <span v-if="detailForm.isGoodsControlMode != null">
                  {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isGoodsControlMode) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem v-if="detailForm.businessStructure !== 'GENERAL'" label="授信额度(元)">
                <span v-if="detailForm.creditAmount">
                  {{ Number(detailForm.creditAmount).toFixed(2) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem v-if="detailForm.businessStructure !== 'GENERAL'" label="合作费率（年%）">
                <span v-if="detailForm.serviceFeeRate">
                  {{ Number(detailForm.serviceFeeRate).toFixed(2) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem v-if="detailForm.businessStructure !== 'GENERAL'" label="账期（天)">
                <span v-if="detailForm.paymentTermDays">
                  {{ detailForm.paymentTermDays }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem v-if="detailForm.businessStructure !== 'GENERAL'" label="结算方式">
                <span v-if="detailForm.settlementMethod">
                  {{ getDictLabel(getDictList('SETTLEMENT_MODE'), detailForm.settlementMethod) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="预计开始日期">
                <span v-if="detailForm.planStartDate">
                  {{ formatDate(detailForm.planStartDate, 'YYYY-MM-DD') }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem v-if="detailForm.businessStructure !== 'GENERAL'" label="授信到期日">
                <span v-if="detailForm.creditDueDate">
                  {{ formatDate(detailForm.creditDueDate, 'YYYY-MM-DD') }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem v-if="detailForm.businessStructure !== 'GENERAL'" label="授信类型">
                <span v-if="detailForm.creditType">
                  {{ getDictLabel(getDictList('CREDIT_TYPE'), detailForm.creditType) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="采购模式">
                <span v-if="detailForm.purchaseMode && detailForm.purchaseMode.length > 0">
                  {{ getMultiDictLabels(getDictList('PURCHASE_MODE'), detailForm.purchaseMode) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="付款方式">
                <span v-if="detailForm.paymentMethod">
                  {{ getMultiDictLabels(getDictList('PAYMENT_WAY'), detailForm.paymentMethod) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="回款方式">
                <span v-if="detailForm.collectionMethod">
                  {{ getMultiDictLabels(getDictList('PAYMENT_WAY'), detailForm.collectionMethod) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="是否支持重点产业链">
                <span v-if="detailForm.isKeyIndustry != null">
                  {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isKeyIndustry) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="是否支持实体企业">
                <span v-if="detailForm.isRealEnterprise != null">
                  {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isRealEnterprise) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="是否支持重点产业链">
                <span v-if="detailForm.isKeyIndustry != null">
                  {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isKeyIndustry) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="是否支持实体企业">
                <span v-if="detailForm.isRealEnterprise != null">
                  {{ getDictLabel(getDictList('baseBooleanType'), detailForm.isRealEnterprise) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="业务负责人">
                <span v-if="detailForm.businessManagerId && detailForm.businessManagerId.length > 0">
                  {{ getManagerNames(detailForm.businessManager) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="运营负责人">
                <span v-if="detailForm.operationManagerId && detailForm.operationManagerId.length > 0">
                  {{ getManagerNames(detailForm.operationManager) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="财务负责人">
                <span v-if="detailForm.financeManagerId && detailForm.financeManagerId.length > 0">
                  {{ getManagerNames(detailForm.financeManager) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="风控负责人">
                <span v-if="detailForm.riskManagerId && detailForm.riskManagerId.length > 0">
                  {{ getManagerNames(detailForm.riskManager) }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
              <DescriptionsItem label="项目地点" :span="2">
                <span v-if="detailForm.province"> {{ detailForm.province }} / </span>
                <span v-if="detailForm.city"> {{ detailForm.city }} / </span>
                <span v-if="detailForm.district">
                  {{ detailForm.district }}
                </span>
                <span v-if="detailForm.detailAddress">
                  {{ detailForm.detailAddress }}
                </span>
                <span
                  v-if="!detailForm.province && !detailForm.city && !detailForm.district && !detailForm.detailAddress"
                >
                  -
                </span>
              </DescriptionsItem>
              <DescriptionsItem label="备注" :span="2">
                <span v-if="detailForm.remarks">
                  {{ detailForm.remarks }}
                </span>
                <span v-else> - </span>
              </DescriptionsItem>
            </Descriptions>

            <!-- 关联企业及敞口信息 -->
            <BasicCaption content="上游企业信息" />
            <div>
              <GridSupplier>
                <template #companyName="{ row }">
                  {{ row.companyName }}
                </template>
                <template #companyCode="{ row }">
                  {{ row.companyCode }}
                </template>
              </GridSupplier>
            </div>
            <BasicCaption content="下游企业信息" />
            <div>
              <GridPurchaser>
                <template #companyName="{ row }">
                  {{ row.companyName }}
                </template>
                <template #companyCode="{ row }">
                  {{ row.companyCode }}
                </template>
              </GridPurchaser>
            </div>
            <BasicCaption content="授信企业信息" />
            <div>
              <GridCredit>
                <template #companyName="{ row }">
                  {{ row.companyName }}
                </template>
                <template #companyCode="{ row }">
                  {{ row.companyCode }}
                </template>
                <template #subLimitAmount="{ row }">
                  {{ row.subLimitAmount }}
                </template>
                <template #occupyLimit="{ row }">
                  <span v-if="row.occupyLimit != null">
                    {{ getDictLabel(getDictList('baseBooleanType'), row.occupyLimit) }}
                  </span>
                </template>
                <template #expiryDate="{ row }">
                  {{ formatDate(row.expiryDate, 'YYYY-MM-DD') }}
                </template>
                <template #creditType="{ row }">
                  <span v-if="row.creditType">
                    {{ getDictLabel(getDictList('CREDIT_TYPE'), row.creditType) }}
                  </span>
                </template>
              </GridCredit>
            </div>

            <!-- 增信措施 -->
            <BasicCaption content="增信措施" />
            <Descriptions v-bind="DESCRIPTIONS_PROP" class="mt-4">
              <DescriptionsItem label="抵押物附件信息" v-if="currentProjectType !== 'initiation'">
                <BaseFileList :model-value="detailForm.mortgageInfoAttachment" />
              </DescriptionsItem>
              <DescriptionsItem label="抵押物信息描述" v-if="currentProjectType !== 'initiation'">
                {{ detailForm.mortgageInfoDesc }}
              </DescriptionsItem>
              <DescriptionsItem label="质押物附件信息" v-if="currentProjectType !== 'initiation'">
                <BaseFileList :model-value="detailForm.pledgeInfoAttachment" />
              </DescriptionsItem>
              <DescriptionsItem label="质押物信息描述" v-if="currentProjectType !== 'initiation'">
                {{ detailForm.pledgeInfoDesc }}
              </DescriptionsItem>
              <DescriptionsItem label="风控措施描述" v-if="currentProjectType !== 'initiation'">
                {{ detailForm.riskControlDesc }}
              </DescriptionsItem>
              <DescriptionsItem label="增信措施描述">
                {{ detailForm.creditEnhancementDesc }}
              </DescriptionsItem>
            </Descriptions>

            <!-- 附件信息 -->
            <BaseAttachmentList
              border="inner"
              v-model="detailForm.attachmentList"
              :business-id="detailForm.id"
              business-type="SCM_PROJECT"
            >
              <template #header>
                <BasicCaption content="附件信息" />
              </template>
            </BaseAttachmentList>
            <BasicCaption content="项目评审记录" />
            <HistoryGrid>
              <template #reviewStatus="{ row, column }">
                <StatusTag
                  v-if="row[column.field]"
                  code="PROJECT_REVIEW_STATUS"
                  :value="row[column.field]"
                  class="cursor-pointer"
                  @click="viewDetail(row, column.field)"
                />
                <a-tag v-else> 未开始 </a-tag>
              </template>
            </HistoryGrid>
          </div>
        </TabPane>
        <template v-if="currentProjectType !== 'change'">
          <TabPane key="archives" tab="项目归档">
            <BaseArchiveList :project-id="detailForm.id" />
          </TabPane>
          <TabPane key="contract" tab="合同信息">
            <ContractList
              v-model="detailForm.contractList"
              :business-id="detailForm.id"
              business-type="PROJECT_CONTRACT"
              edit-mode
            >
              <template #header> </template>
            </ContractList>
          </TabPane>
          <TabPane key="order" tab="订单信息" />
          <TabPane key="finance" tab="收付款信息" />
          <TabPane key="invoice" tab="发票信息" />
        </template>
      </Tabs>
    </div>
  </BasicPopup>
</template>

<style scoped>
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}

.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
