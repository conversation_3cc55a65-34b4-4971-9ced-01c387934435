<script setup lang="ts">
import type { ABSManageInfo } from '#/api/abs-manage';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { detailABSApi } from '#/api/abs-manage';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

defineEmits(['register']);

const { useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading, isProcessInstance } =
  useWorkflowBase();
const pageType = ref('detail');
const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const absDetail = ref<ABSManageInfo>({});
const init = async (data: ABSManageInfo) => {
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_abs', businessKey: data.id });
  if (data?.id) {
    absDetail.value = await detailABSApi(data.id);
  }
};
const [registerPopup, { closePopup }] = usePopupInner(init);

const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
</script>

<template>
  <BasicPopup v-bind="$attrs" title="ABS信息" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="ABS编号">
          {{ absDetail.absProjectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="ABS名称">
          {{ absDetail.absProjectName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ absDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="日期">
          {{ absDetail.projectDate }}
        </a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ absDetail.remark }}
        </a-descriptions-item>
      </a-descriptions>
      <ProducGrid />
      <BaseAttachmentList border="inner" :key="absDetail.id" :business-id="absDetail.id" business-type="SCM_ABS" />
    </div>
  </BasicPopup>
</template>

<style></style>
