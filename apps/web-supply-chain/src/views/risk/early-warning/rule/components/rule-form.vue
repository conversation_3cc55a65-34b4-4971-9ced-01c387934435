<script setup lang="ts">
import type { EarlyWarningRuleInfo } from '#/api';

import { computed, ref, unref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import { defaultsDeep } from 'lodash-es';

import { BaseFeUserSelect } from '#/adapter/fe-ui';
import { editEarlyWarningRuleApi } from '#/api';

const { getDictList } = useDictStore();
const ruleFormRef = ref();
const ruleForm = ref<Partial<EarlyWarningRuleInfo>>({
  smsUserIdList: [],
});

const modalTitle = ref('告警规则配置');
const valueTypeConfig = {
  '1': {
    after: '天',
  },
  '2': {
    after: '%',
  },
};
// const addonBefore = computed(() => {
//   const valueType = ruleForm.value.valueType;
//   if (valueType && (valueType === '1' || valueType === '2')) {
//     return valueTypeConfig[valueType].before;
//   }
//   return '';
// });

const addonAfter = computed(() => {
  const valueType = ruleForm.value.valueType;
  if (valueType && (valueType === '1' || valueType === '2')) {
    return valueTypeConfig[valueType].after;
  }
  return '';
});
const value = computed({
  get() {
    return ruleForm.value.valueType === '2'
      ? new BigNumber(ruleForm.value.value ?? 0).times(100).toNumber()
      : new BigNumber(ruleForm.value.value ?? 0).toNumber();
  },
  set(newValue: number) {
    ruleForm.value.value = ruleForm.value.valueType === '2' ? new BigNumber(newValue).div(100).toNumber() : newValue;
  },
});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {
    const data = modalApi.getData() ?? {};
    ruleForm.value = defaultsDeep(data, { smsUserIdList: [] });
  },
  onConfirm: async () => {
    await ruleFormRef.value.validate();
    if ((!ruleForm.value.smsUserIdList || ruleForm.value.smsUserIdList.length === 0) && !ruleForm.value.notifyRole) {
      message.error('预警项目角色和预警指定人员不能同时为空');
      return;
    }
    try {
      modalApi.lock();
      await editEarlyWarningRuleApi(unref(ruleForm) as EarlyWarningRuleInfo);
      message.success('保存成功');
      await modalApi.close();
    } finally {
      modalApi.unlock();
    }
  },
  onBeforeClose: () => {
    ruleForm.value = {};
    ruleFormRef.value.resetFields();
    return true;
  },
});

const ruleRules = {
  value: [{ required: true, message: '请输入预警触发参数' }],
  warningLevel: [{ required: true, message: '请选择预警等级', trigger: 'change' }],
};
</script>

<template>
  <Modal :title="modalTitle">
    <a-form
      ref="ruleFormRef"
      :model="ruleForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :colon="false"
      :rules="ruleRules"
    >
      <a-form-item label="预警触发参数" name="value">
        <a-input-number v-model:value="value" class="w-full" :min="0" :controls="false">
          <!--<template #addonBefore> {{ addonBefore }} </template>-->
          <template #addonAfter> {{ addonAfter }} </template>
        </a-input-number>
      </a-form-item>
      <a-form-item label="预警等级" name="warningLevel">
        <a-select v-model:value="ruleForm.warningLevel" :options="getDictList('RISK_LEVEL')" />
      </a-form-item>
      <a-form-item label="预警项目角色" name="notifyRoleList">
        <a-select
          v-model:value="ruleForm.notifyRoleList"
          :options="getDictList('INFORM_STAFF_TYPE')"
          allow-clear
          mode="multiple"
        />
      </a-form-item>
      <a-form-item label="预警指定人员" name="smsUserIdList">
        <BaseFeUserSelect v-model:value="ruleForm.smsUserIdList" multiple />
      </a-form-item>
      <a-form-item label="短信提醒" name="isSms">
        <a-switch v-model:checked="ruleForm.isSms" :checked-value="1" :un-checked-value="0" />
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style></style>
