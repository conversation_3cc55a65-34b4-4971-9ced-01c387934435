<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { EarlyWarningItemInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { DESCRIPTIONS_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getEarlyWarningItemDetailApi, handleEarlyWarningItemApi } from '#/api';

const emit = defineEmits(['ok', 'register']);

const state = reactive({
  pageType: 'edit',
});

const pageTitle = computed(() => {
  return state.pageType === 'detail' ? '预警详情' : '预警处置';
});
const warningItemForm = ref<EarlyWarningItemInfo>({});
const init = async ({ data, pageType }: { data: EarlyWarningItemInfo; pageType: 'detail' | 'edit' }) => {
  state.pageType = pageType;
  if (data.id) {
    warningItemForm.value = await getEarlyWarningItemDetailApi({ id: data.id });
    await gridApi.grid.loadData(warningItemForm.value.warningRecordLogList ?? []);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  changeOkLoading(true);
  try {
    await handleEarlyWarningItemApi({ ...handleForm.value, id: warningItemForm.value.id });
    message.success('保存成功');
    closePopup();
    emit('ok');
  } finally {
    changeOkLoading(false);
  }
};
const { getDictList } = useDictStore();
const gridOptions: VxeTableGridOptions<EarlyWarningItemInfo> = {
  showOverflow: 'title',
  keepSource: true,
  columns: [
    {
      field: 'seq',
      type: 'seq',
      width: 80,
    },
    { field: 'triggerDate', title: '触发时间', formatter: 'formatDateTime', width: 180 },
    { field: 'triggerReason', title: '触发记录' },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const handleForm = ref<EarlyWarningItemInfo>({});
const rules = {
  disposalMethod: [{ required: true, message: '请选择处置措施', trigger: 'change' }],
  disposalUser: [{ required: true, message: '请输入处置人', trigger: 'blur' }],
};
const formProp = { ...FORM_PROP };
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    :show-ok-btn="state.pageType !== 'detail'"
    :title="pageTitle"
    ok-text="提交"
    @register="registerPopup"
    @ok="save"
  >
    <div class="mx-4">
      <BasicCaption content="预警规则信息" />
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="预警规则名称">
          {{ warningItemForm.ruleName }}
        </a-descriptions-item>
        <a-descriptions-item label="预警记录编号">
          {{ warningItemForm.recordCode }}
        </a-descriptions-item>
        <a-descriptions-item label="风险等级">
          <StatusTag code="RISK_LEVEL" :value="warningItemForm.warningRuleBO?.warningLevel" />
        </a-descriptions-item>
        <a-descriptions-item label="关联业务编号">
          {{ warningItemForm.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="关联业务">
          {{ warningItemForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="预警企业名称">
          {{ warningItemForm.companyName }}
        </a-descriptions-item>
        <a-descriptions-item label="触发原因" :span="2">
          {{ warningItemForm.lastTriggerReason }}
        </a-descriptions-item>
        <a-descriptions-item label="预警规则描述" :span="2">
          {{ warningItemForm.ruleDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="预警触发条件" :span="2">
          {{ warningItemForm.warningRuleBO?.conditionDesc }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="预警触发记录" />
      <Grid />
      <template v-if="warningItemForm.disposalStatus === 'DISPOSED'">
        <BasicCaption content="预警处置" />
        <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
          <a-descriptions-item label="处置措施">
            <StatusTag code="DISPOSAL_TYPE" :value="warningItemForm.disposalMethod" />
          </a-descriptions-item>
          <a-descriptions-item label="处置人">
            {{ warningItemForm.disposalUser }}
          </a-descriptions-item>
          <a-descriptions-item label="处置说明" :span="2">
            {{ warningItemForm.disposalDescription }}
          </a-descriptions-item>
        </a-descriptions>
      </template>
      <template v-if="state.pageType === 'edit' && warningItemForm.disposalStatus === 'UNDISPOSED'">
        <BasicCaption content="预警处置" />
        <a-form
          v-if="state.pageType === 'edit'"
          class="mt-4"
          ref="formRef"
          :model="handleForm"
          :rules="rules"
          v-bind="formProp"
        >
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="处置措施" name="disposalMethod">
                <a-select v-model:value="handleForm.disposalMethod" :options="getDictList('DISPOSAL_TYPE')" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="处置人" name="disposalUser">
                <a-input v-model:value="handleForm.disposalUser" />
              </a-form-item>
            </a-col>
            <a-col :span="24">
              <a-form-item label="处置说明" name="disposalDescription" v-bind="FULL_FORM_ITEM_PROP">
                <a-textarea v-model:value="handleForm.disposalDescription" :rows="4" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
      <BaseAttachmentList
        v-model="handleForm.attachmentList"
        :business-id="warningItemForm.id"
        business-type="RSK_WARNING_RECORD"
        :edit-mode="state.pageType === 'edit'"
      />
    </div>
  </BasicPopup>
</template>

<style scoped></style>
