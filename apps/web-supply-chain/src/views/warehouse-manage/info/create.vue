<script setup lang="ts">
import { computed, ref, reactive, watch } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {
  addTransportApi,
  type AddWarehouse,
  addWarehouseApi,
  editTransportApi,
  editWarehouseApi,
  getCompanyApi,
  submitTransportApi,
  type TransOrderBaseInfo,
  warehouseDetailApi,
  warehouseSubmitApi,
} from '#/api';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, defaultsDeep } from '@vben/utils';

import { Col, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';
import { BaseAttachmentList, BaseRegionPicker } from '#/adapter/base-ui';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const { startWorkflow, WorkflowPreviewModal, initWorkflow } = useWorkflowBase();
const pageType = ref('edit');

const { getDictList } = useDictStore();
const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};
const loading = reactive({
  submit: false,
});
// 默认数据
const defaultForm: Partial<AddWarehouse> = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  warehouseCode: undefined,
  warehouseName: undefined,
  warehouseCompanyId: undefined,
  warehouseCompanyName: undefined,
  warehouseCompanyCode: undefined,
  warehouseType: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  status: undefined,
  remarks: undefined,
  warehouseStatus: undefined,
  attachmentList: [],
};

const detailForm = reactive<Partial<AddWarehouse>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  warehouseName: [{ required: true, message: '请输入仓库名称', trigger: 'change' }],
  warehouseType: [{ required: true, message: '请选择仓库类型', trigger: 'change' }],
  warehouseCompanyId: [{ required: true, message: '请选择所属公司', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.id ? '编辑仓库' : '新增仓库';
});

const init = async (data: any) => {
  await getCompanyList();
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_warehouse', businessKey: data.id });
  if (data.id) {
    const res = await warehouseDetailApi(data.id); // 调用真实 API
    Object.assign(detailForm, res);
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};
const formRef = ref();
const save = async (type: 'submit' | 'save') => {
  try {
    await formRef.value.validate();
    changeOkLoading(true);

    let api = detailForm.id ? editWarehouseApi : addWarehouseApi;
    if (type === 'submit') {
      api = warehouseSubmitApi;
    }

    if (type === 'submit') {
      const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
      detailForm.processDefinitionKey = processDefinitionKey;
      detailForm.startUserSelectAssignees = startUserSelectAssignees;
    }

    const submitData = detailForm as Required<AddWarehouse>;
    const res = await api(submitData);

    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false);
  } finally {
    changeOkLoading(false);
  }
  close();
};

const labelCol = { style: { width: '150px' } };

watch(
  () => detailForm.warehouseCompanyId,
  (val) => {
    detailForm.warehouseCompanyCode = companyOptions.value.find((item) => item.id === val)?.companyCode;
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" @close="close">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')"> 保存 </a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')"> 提交 </a-button>
      </a-space>
    </template>
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <!-- 仓库编号 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库编号" name="warehouseCode">
            <Input v-model:value="detailForm.warehouseCode" placeholder="提交后自动生成" :disabled="!!detailForm.id" />
          </FormItem>
        </Col>

        <!-- 仓库名称 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库名称" name="warehouseName">
            <Input v-model:value="detailForm.warehouseName" />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="所属仓储运营方" name="warehouseCompanyId">
            <Select
              v-model:value="detailForm.warehouseCompanyId"
              :options="companyOptions"
              :field-names="{ label: 'companyName', value: 'id' }"
              show-search
              :filter-option="
                (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
              "
            />
          </FormItem>
        </Col>

        <Col v-bind="colSpan">
          <FormItem label="统一社会信用代码" name="warehouseCompanyCode">
            <span v-if="detailForm.warehouseCompanyCode">{{ detailForm.warehouseCompanyCode }}</span>
            <span v-else>-</span>
          </FormItem>
        </Col>

        <!-- 仓库类型 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库类型" name="warehouseType">
            <Select v-model:value="detailForm.warehouseType" :options="getDictList('WAREHOUSE_TYPE')" />
          </FormItem>
        </Col>

        <!-- 仓库地址 占一行 -->
        <Col v-bind="colSpan">
          <Row :gutter="12">
            <Col :span="12">
              <FormItem label="仓库地址">
                <BaseRegionPicker
                  v-model:province="detailForm.province"
                  v-model:city="detailForm.city"
                  v-model:district="detailForm.district"
                />
              </FormItem>
            </Col>
            <Col :span="12">
              <FormItem name="detailAddress">
                <Input v-model:value="detailForm.detailAddress" />
              </FormItem>
            </Col>
          </Row>
        </Col>

        <!-- 备注 -->
        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>
      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_WAREHOUSE"
        edit-mode
      />
    </Form>
    <WorkflowPreviewModal v-if="pageType === 'edit'" />
  </BasicPopup>
</template>

<style scoped></style>
