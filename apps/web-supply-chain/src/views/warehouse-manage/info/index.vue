<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';
import { VbenIcon } from '@vben-core/shadcn-ui';

import { type AddWarehouse, getWarehousePageApi, warehouseEnableApi, warehouseDisableApi, getCompanyApi } from '#/api';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, message, Space, TypographyLink } from 'ant-design-vue';
import { useModalUrl } from '@vben/base-ui';

import Create from './create.vue';
import Detail from './detail.vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';
import { onMounted, ref } from 'vue';

const { getDictList } = useDictStore();

const sortKey = ref<string>('create_time');

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'warehouseCode',
      label: '仓库编号',
    },
    {
      component: 'Input',
      fieldName: 'warehouseName',
      label: '仓库名称',
    },
    {
      component: 'Select',
      fieldName: 'warehouseStatus',
      label: '仓库状态',
      componentProps: {
        options: getDictList('WAREHOUSE_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'warehouseCompanyName',
      label: '所属仓储运营方',
    },
    {
      component: 'Select',
      fieldName: 'warehouseType',
      label: '仓库类型',
      componentProps: {
        options: getDictList('WAREHOUSE_TYPE'),
      },
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-3',
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'warehouseCode', title: '仓库编号' },
    { field: 'warehouseName', title: '仓库名称' },
    {
      field: 'warehouseCompanyId',
      title: '所属仓储运营方',
      formatter: ({ cellValue }) => {
        if (cellValue) {
          return companyOptions.value.find((item) => item.id === cellValue)?.companyName;
        }
      },
    },
    {
      field: 'warehouseType',
      title: '仓库类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'WAREHOUSE_TYPE',
        },
      },
    },
    {
      field: 'warehouseStatus',
      title: '仓库状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'WAREHOUSE_STATUS',
        },
      },
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    {
      field: 'status',
      title: '业务状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getWarehousePageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetailForm, { openPopup: openDetailPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const add = () => {
  openFormPopup(true, { pageType: 'edit' });
};

const edit = (row: AddWarehouse) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};

const detail = (row: AddWarehouse) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};

const active = async () => {
  const rows = gridApi.grid.getCheckboxRecords(true);
  if (!rows) {
    message.error('请至少选择一条数据');
    return;
  }
  const ids = rows.map((item) => item.id);
  AntdModal.confirm({
    title: '确认启用',
    content: '此操作将启用已勾选的仓库，是否继续？',
    async onOk() {
      await warehouseEnableApi(ids);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const inactive = async () => {
  const rows = gridApi.grid.getCheckboxRecords(true);
  if (!rows) {
    message.error($t('请至少选择一条数据'));
    return;
  }
  const ids = rows.map((item) => item.id);
  AntdModal.confirm({
    title: '确认启用',
    content: '此操作将禁用已勾选的仓库，是否继续？',
    async onOk() {
      await warehouseDisableApi(ids);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};

const access = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'audit' });
};

useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data: AccessInfo = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data: AccessInfo = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});

onMounted(async () => {
  await getCompanyList();
});
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
        <Button class="mr-2" type="primary" @click="active">
          {{ $t('base.active') }}
        </Button>
        <Button class="mr-2" danger type="primary" @click="inactive">
          {{ $t('base.inactive') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetailForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
