<script setup lang="ts">
import type { AddWarehouse } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Descriptions, DescriptionsItem } from 'ant-design-vue';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getCompanyApi, warehouseDetailApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const { useOperationButton, useViewButton, initWorkflow, isWorkflow, isProcessInstance, isWorkflowLoading } =
  useWorkflowBase();
const pageType = ref('detail');
const OperationButton = useOperationButton();
const ViewButton = useViewButton();

const { getDictList } = useDictStore();

// 默认数据
const defaultForm: Partial<AddWarehouse> = {
  id: undefined,
  createTime: '',
  createBy: 0,
  updateTime: '',
  updateBy: 0,
  version: 0,
  warehouseCode: '',
  warehouseName: '',
  warehouseCompanyId: 0,
  warehouseCompanyName: '',
  warehouseCompanyCode: '',
  warehouseType: '',
  province: '',
  city: '',
  district: '',
  detailAddress: '',
  status: '',
  remarks: '',
};

const detailForm = reactive<Partial<AddWarehouse>>(defaultsDeep(defaultForm));

const title = '仓库详细信息';

const companyOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

const init = async (data: any) => {
  await getCompanyList();
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_warehouse', businessKey: data.id });
  if (data.id) {
    const res = await warehouseDetailApi(data.id); // 调用真实 API
    Object.assign(detailForm, res);
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
  }
};

const workflowSuccess = () => {
  emit('ok');
  closePopup();
};

const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="仓库编号">
          <span v-if="detailForm.warehouseCode"> {{ detailForm.warehouseCode }} </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="仓库名称">
          <span v-if="detailForm.warehouseName"> {{ detailForm.warehouseName }} </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="所属仓储运营方">
          <span v-if="detailForm.warehouseCompanyId">
            {{ companyOptions.find((item) => item.id === detailForm.warehouseCompanyId).companyName }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="统一社会信用代码">
          <span v-if="detailForm.warehouseCompanyCode">
            {{ detailForm.warehouseCompanyCode }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="仓库类型">
          <span v-if="detailForm.warehouseType">
            {{ getDictList('WAREHOUSE_TYPE').find((item) => item.value === detailForm.warehouseType).label }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>

        <DescriptionsItem label="仓库地址" :span="2">
          <span v-if="detailForm.province"> {{ detailForm.province }} / </span>
          <span v-if="detailForm.city"> {{ detailForm.city }} / </span>
          <span v-if="detailForm.district">
            {{ detailForm.district }}
          </span>
          <span v-if="detailForm.detailAddress" class="ml-2">
            {{ detailForm.detailAddress }}
          </span>
          <span v-if="!detailForm.province && !detailForm.city && !detailForm.district && !detailForm.detailAddress">
            -
          </span>
        </DescriptionsItem>

        <DescriptionsItem label="备注">
          <span v-if="detailForm.remarks">
            {{ detailForm.remarks }}
          </span>
          <span v-else> - </span>
        </DescriptionsItem>
      </Descriptions>

      <BaseAttachmentList
        border="inner"
        v-model="detailForm.attachmentList"
        :business-id="detailForm.id"
        business-type="SCM_WAREHOUSE"
      />
    </div>
  </BasicPopup>
</template>

<style scoped></style>
