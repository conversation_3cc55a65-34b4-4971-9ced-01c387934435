<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ShipmentBaseInfo } from '#/api';

import { computed, nextTick, reactive, ref, watch } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { BaseAttachmentList, BaseRegionPicker } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addShipmentApi,
  detailShipmentApi,
  editShipmentApi,
  getCompanyApi,
  getOrderCodesApi,
  projectManageList<PERSON>pi,
  queryItemListApi,
} from '#/api';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();

interface SelectOption {
  projectName?: string;
  id?: number | string;
  projectCode?: string;
  executorCompanyName?: string;
}

const belongProjectOptions = ref<SelectOption[]>([]);

// 默认数据
const defaultForm: Partial<ShipmentBaseInfo> = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  shipmentCode: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  shipmentType: undefined,
  transportMethod: undefined,
  carrierCompanyCode: undefined,
  carrierCompanyName: undefined,
  consigneeCompanyCode: undefined,
  consigneeCompanyName: undefined,
  billingCompanyCode: undefined,
  billingCompanyName: undefined,
  shipmentDate: undefined,
  status: undefined,
  totalShipmentCost: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  plannedDeliveryDate: undefined,
  receiptDistrict: undefined,
  receiptDetailAddress: undefined,
  receiptProvince: undefined,
  receiptCity: undefined,
  remarks: undefined,
  shipmentDeliveryList: [],
  shipmentItemList: [],
  shipmentSourceRelList: [],
  attachmentList: [],
  documentType: undefined,
};
const shipmentSourceRelList = ref([]);
let detailForm = reactive<Partial<ShipmentBaseInfo>>(cloneDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  shipmentType: [{ required: true, message: '请选择运输类型', trigger: 'change' }],
  transportMethod: [{ required: true, message: '请选择运输方式', trigger: 'change' }],
  projectName: [{ required: true, message: '请输入所属项目名称', trigger: 'change' }],
  documentType: [{ required: true, message: '请选择关联单据类型', trigger: 'change' }],
  shipmentSourceRelList: [{ required: true, message: '请选择关联单据', trigger: 'change' }],
  consigneeCompanyCode: [{ required: true, message: '收货企业', trigger: 'change' }],
};

const companyOptions = ref([]);
const orderCodesOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};

// 获取关联单据列表
const getOrderCodesList = async () => {
  if (detailForm.documentType === 'PICKUP_REQUEST') {
    message.error('先不要选提货单,接口没写好,选别的测吧');
    return;
  }
  const res = await getOrderCodesApi({
    orderType: detailForm.documentType,
  });
  Object.assign(orderCodesOptions.value, res);
  console.log(orderCodesOptions.value);
};

const title = computed(() => {
  return detailForm.id ? '编辑发货单' : '新增发货单';
});

const init = async (data: any) => {
  const res = await projectManageListApi({ projectName: '' });
  belongProjectOptions.value = res.map((item) => ({
    projectName: item.projectName,
    id: item.id,
  }));
  await getCompanyList();
  if (data.id) {
    const res = await detailShipmentApi(data.id);
    Object.assign(detailForm, res);

    // 强制刷新表格数据
    setTimeout(() => {
      gridApi.grid.loadData(detailForm.shipmentDeliveryList || []);
      gridApiGoods.grid.loadData(detailForm.shipmentItemList || []);
    }, 0);
  } else {
    detailForm = cloneDeep(defaultForm);
    // 清空表格数据
    setTimeout(() => {
      gridApi.grid.loadData([]);
      gridApiGoods.grid.loadData([]);
    }, 0);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);

    // 手动同步表格数据
    const deliveryTableData = gridApi.grid.getRecordset();
    const goodsTableData = gridApiGoods.grid.getRecordset();

    // 更新 detailForm 中的数据
    detailForm.shipmentDeliveryList = deliveryTableData.insertRecords.concat(deliveryTableData.updateRecords);
    detailForm.shipmentItemList = goodsTableData.insertRecords.concat(goodsTableData.updateRecords);

    const formData = {
      ...detailForm,
      shipmentDeliveryList: detailForm.shipmentDeliveryList || [],
      shipmentItemList: detailForm.shipmentItemList || [],
      shipmentSourceRelList: detailForm.shipmentSourceRelList || [],
    } as ShipmentBaseInfo;

    const res = detailForm.id ? await editShipmentApi(formData) : await addShipmentApi(formData);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
    close();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const labelCol = { style: { width: '150px' } };
const grid: VxeTableGridOptions = {
  data: detailForm.shipmentDeliveryList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'deliveryType',
      title: '运载工具',
      slots: { default: 'deliveryType' },
      minWidth: '150px',
    },
    {
      field: 'deliveryNumber',
      title: '车辆号/船舶号/物流号',
      slots: { default: 'deliveryNumber' },
      minWidth: '160px',
    },
    {
      field: 'contactName',
      title: '联系人姓名',
      slots: { default: 'contactName' },
      minWidth: '150px',
    },
    {
      field: 'contactPhone',
      title: '联系人电话',
      slots: { default: 'contactPhone' },
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', slots: { default: 'remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const gridGoods: VxeTableGridOptions = {
  data: detailForm.shipmentItemList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      slots: { default: 'productName' },
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { default: 'productAlias' },
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { default: 'productCode' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { default: 'specifications' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      slots: { default: 'measureUnit' },
      minWidth: '150px',
    },
    {
      field: 'brandName',
      title: '商品品牌',
      slots: { default: 'brandName' },
      minWidth: '150px',
    },
    {
      field: 'originName',
      title: '生产厂家',
      slots: { default: 'originName' },
      minWidth: '150px',
    },
    {
      field: 'shippedQuantity',
      title: '本次发运重量',
      slots: { default: 'shippedQuantity' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentItemNumber',
      title: '源单据商品行号',
      slots: { default: 'sourceDocumentItemNumber' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentName',
      title: '源单据名称',
      slots: { default: 'sourceDocumentName' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentCode',
      title: '源单据编号',
      slots: { default: 'sourceDocumentCode' },
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', slots: { default: 'remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const selectedProjectCode = ref<string>('');
const selectedExecutorCompanyName = ref<string>('');
const handleProjectChange = (id: string) => {
  belongProjectOptions.value.map((item) => {
    if (item.id === id) {
      selectedProjectCode.value = item.id!.toString();
      selectedExecutorCompanyName.value = item.executorCompanyName!;
    }
  });
};

const handleDocumentTypeChange = async () => {
  await getOrderCodesList();
};

// 新增行
const addLocationRow = async (gridApi: GridApi) => {
  const newRecord = {
    id: detailForm.shipmentDeliveryList!.length,
    version: undefined,
    shipmentId: undefined,
    deliveryType: undefined,
    deliveryNumber: undefined,
    contactName: undefined,
    contactPhone: undefined,
    remarks: undefined,
  };
  const $grid = gridApi.grid;
  if ($grid) {
    await $grid.insertAt(newRecord, -1);
  }
};

// 删除行
const removeLocationRow = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectedRows = $grid.getCheckboxRecords();
    if (selectedRows.length > 0) {
      $grid.remove(selectedRows);
      message.success('删除成功');
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

const resetGoods = () => {
  detailForm.shipmentItemList = [];
  if (gridApiGoods.grid) {
    gridApiGoods.grid.loadData([]);
  }
};

const close = () => {
  Object.assign(detailForm, cloneDeep(defaultForm));
};

// 注册表格
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: grid,
});

const [GridGoods, gridApiGoods] = useVbenVxeGrid({
  gridOptions: gridGoods,
});

const handleOrderChange = async (_: any, option: any) => {
  // 确保 shipmentSourceRelList 存在
  if (!detailForm.shipmentSourceRelList) {
    detailForm.shipmentSourceRelList = [];
  }

  // 如果 option 是数组（多选模式），则处理所有选项
  if (Array.isArray(option)) {
    // 清空原数组
    detailForm.shipmentSourceRelList = [];

    // 添加所有选中的选项，自动去重
    option.forEach((opt: any) => {
      detailForm.shipmentSourceRelList!.push({
        sourceDocumentId: opt.id,
        sourceDocumentCode: opt.orderCode,
        sourceDocumentName: opt.orderName,
        sourceDocumentType: detailForm.documentType,
      });
    });
  } else {
    // 单选情况
    const existingIndex = detailForm.shipmentSourceRelList.findIndex(
      (item: any) => item.sourceDocumentId === option.id,
    );

    if (existingIndex === -1) {
      detailForm.shipmentSourceRelList.push({
        sourceDocumentId: option.id,
        sourceDocumentCode: option.orderCode,
        sourceDocumentName: option.orderName,
        sourceDocumentType: detailForm.documentType,
      });
    }
  }

  if (detailForm.documentType && detailForm.documentType.trim() !== '') {
    try {
      const itemList = await queryItemListApi({
        orderType: detailForm.documentType,
        orderIds: detailForm.shipmentSourceRelList.map((item: any) => item.sourceDocumentId),
      });

      // 更新数据并确保响应式
      detailForm.shipmentItemList = Array.isArray(itemList) ? itemList : [];

      // 强制刷新表格数据
      if (gridApiGoods.grid) {
        await gridApiGoods.grid.loadData(detailForm.shipmentItemList);
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
      message.error('获取商品列表失败');
    }
  }
};

// 监听 shipmentDeliveryList 变化并更新表格
watch(
  () => detailForm.shipmentDeliveryList,
  (newVal) => {
    if (gridApi.grid) {
      nextTick(() => {
        gridApi.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);

// 监听 shipmentItemList 变化并更新表格
watch(
  () => detailForm.shipmentItemList,
  (newVal) => {
    if (gridApiGoods.grid) {
      nextTick(() => {
        gridApiGoods.grid.loadData(newVal || []);
      });
    }
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save" @close="close">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Form
        ref="formRef"
        :colon="false"
        :model="detailForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="{ span: 20 }"
        class="px-8"
      >
        <!-- 基本信息 -->
        <BasicCaption content="基本信息" />

        <Row class="mt-5">
          <!-- 发货单编号 -->
          <Col v-bind="colSpan">
            <FormItem label="发货单编号" name="shipmentCode">
              <span v-if="detailForm.id"> {{ detailForm.shipmentCode }} </span>
              <span v-else> - </span>
            </FormItem>
          </Col>

          <!-- 运输企业 -->
          <Col v-bind="colSpan">
            <FormItem label="运输企业" name="carrierCompanyCode">
              <Select
                v-model:value="detailForm.carrierCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
              />
            </FormItem>
          </Col>

          <!-- 运输方式 -->
          <Col v-bind="colSpan">
            <FormItem label="运输方式" name="transportMethod">
              <Select v-model:value="detailForm.transportMethod" :options="getDictList('TRANSPORT_MODE')" />
            </FormItem>
          </Col>

          <!-- 运输类型 -->
          <Col v-bind="colSpan">
            <FormItem label="运输类型" name="shipmentType">
              <Select v-model:value="detailForm.shipmentType" :options="getDictList('TRANSPORT_TYPE')" />
            </FormItem>
          </Col>

          <!-- 所属项目名称 -->
          <Col v-bind="colSpan">
            <FormItem label="所属项目名称" name="projectName">
              <Select
                v-model:value="detailForm.projectName"
                :options="belongProjectOptions"
                show-search
                :field-names="{ label: 'projectName', value: 'id' }"
                :filter-option="(input: string, option: any) => option.projectName.includes(input)"
                @change="handleProjectChange"
              />
            </FormItem>
          </Col>

          <!-- 所属项目编号 -->
          <Col v-bind="colSpan">
            <FormItem label="所属项目编号" name="projectCode">
              <span>{{ detailForm.projectCode || selectedProjectCode || '-' }}</span>
            </FormItem>
          </Col>

          <!-- 发运日期 -->
          <Col v-bind="colSpan">
            <FormItem label="发运日期" name="shipmentDate">
              <DatePicker
                v-model:value="detailForm.shipmentDate"
                value-format="YYYY-MM-DD hh:mm:ss"
                format="YYYY-MM-DD"
              />
            </FormItem>
          </Col>

          <!-- 预计收货日期 -->
          <Col v-bind="colSpan">
            <FormItem label="预计收货日期" name="plannedDeliveryDate">
              <DatePicker
                v-model:value="detailForm.plannedDeliveryDate"
                value-format="YYYY-MM-DD hh:mm:ss"
                format="YYYY-MM-DD"
              />
            </FormItem>
          </Col>

          <!-- 关联单据类型 -->
          <Col v-bind="colSpan">
            <FormItem label="关联单据类型" name="documentType">
              <Select
                v-model:value="detailForm.documentType"
                :options="getDictList('DOCUMENT_TYPE')"
                @change="handleDocumentTypeChange"
              />
            </FormItem>
          </Col>

          <!-- 关联单据 -->
          <Col v-bind="colSpan">
            <FormItem label="关联单据" name="shipmentSourceRelList">
              <Select
                v-model:value="shipmentSourceRelList"
                mode="multiple"
                :options="orderCodesOptions"
                show-search
                :field-names="{ label: 'orderName', value: 'id' }"
                :filter-option="(input: string, option: any) => option.orderName.includes(input)"
                @change="handleOrderChange"
              />
            </FormItem>
          </Col>

          <!-- 收货企业 -->
          <Col v-bind="colSpan">
            <FormItem label="收货企业" name="consigneeCompanyCode">
              <Select
                v-if="companyOptions && companyOptions.length > 0"
                v-model:value="detailForm.consigneeCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
              />
            </FormItem>
          </Col>

          <!-- 结算公司	 -->
          <Col v-bind="colSpan">
            <FormItem label="结算公司" name="billingCompanyCode">
              <Select
                v-model:value="detailForm.billingCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
              />
            </FormItem>
          </Col>

          <!-- 贸易执行企业 -->
          <Col v-bind="colSpan">
            <FormItem label="贸易执行企业" name="executorCompanyName">
              <span> {{ detailForm.executorCompanyName || selectedExecutorCompanyName || '-' }} </span>
            </FormItem>
          </Col>

          <!-- 发货地址 -->
          <Col v-bind="colSpan">
            <Row :gutter="12">
              <Col :span="12">
                <FormItem label="发货地址" name="detailAddress">
                  <BaseRegionPicker
                    v-model:province="detailForm.province"
                    v-model:city="detailForm.city"
                    v-model:district="detailForm.district"
                    :disabled="!!detailForm.id"
                  />
                </FormItem>
              </Col>
              <Col :span="12">
                <FormItem name="detailAddress">
                  <Input v-model:value="detailForm.detailAddress" :disabled="!!detailForm.id" />
                </FormItem>
              </Col>
            </Row>
          </Col>

          <!-- 收货地址 -->
          <Col v-bind="colSpan">
            <Row :gutter="12">
              <Col :span="12">
                <FormItem label="收货地址" name="receiptDetailAddress">
                  <BaseRegionPicker
                    v-model:province="detailForm.receiptProvince"
                    v-model:city="detailForm.receiptCity"
                    v-model:district="detailForm.receiptDistrict"
                    :disabled="!!detailForm.id"
                  />
                </FormItem>
              </Col>
              <Col :span="12">
                <FormItem name="receiptDetailAddress">
                  <Input v-model:value="detailForm.receiptDetailAddress" :disabled="!!detailForm.id" />
                </FormItem>
              </Col>
            </Row>
          </Col>

          <!-- 备注 -->
          <Col v-bind="colSpan">
            <FormItem label="备注" name="remarks">
              <Textarea v-model:value="detailForm.remarks" :rows="3" />
            </FormItem>
          </Col>
        </Row>

        <!-- 物流运输信息 -->
        <BasicCaption content="物流运输信息" />
        <div>
          <Grid>
            <template #toolbar_location_tools>
              <Button class="mr-2" type="primary" @click="() => addLocationRow(gridApi)">增行</Button>
              <Button class="mr-2" danger @click="() => removeLocationRow(gridApi)">删行</Button>
            </template>

            <template #deliveryType="{ row }">
              <Select
                :options="getDictList('TRANSPORT_VEHICLE')"
                v-model:value="row.deliveryType"
                placeholder="请选择运载工具"
              />
            </template>

            <template #deliveryNumber="{ row }">
              <Input v-model:value="row.deliveryNumber" placeholder="请输入车辆号/船舶号/物流号" />
            </template>

            <template #contactName="{ row }">
              <Input v-model:value="row.contactName" placeholder="请输入联系人姓名" />
            </template>

            <template #contactPhone="{ row }">
              <Input v-model:value="row.contactPhone" placeholder="请输入联系人电话" />
            </template>

            <template #remarks="{ row }">
              <Input v-model:value="row.remarks" placeholder="请输入备注" />
            </template>
          </Grid>
        </div>

        <!-- 商品信息 -->
        <BasicCaption content="商品信息" />
        <div>
          <GridGoods>
            <template #toolbar_location_tools>
              <Button class="mr-2" type="primary" @click="() => resetGoods()">重置</Button>
              <Button class="mr-2" danger @click="() => removeLocationRow(gridApiGoods)">删行</Button>
            </template>

            <template #productName="{ row }">
              {{ row.productName }}
            </template>

            <template #productAlias="{ row }">
              {{ row.productAlias }}
            </template>

            <template #productCode="{ row }">
              {{ row.productCode }}
            </template>

            <template #specifications="{ row }">
              {{ row.specifications }}
            </template>

            <template #measureUnit="{ row }">
              {{ row.measureUnit }}
            </template>

            <template #brandName="{ row }"> {{ row.brandName }} </template>

            <template #originName="{ row }"> {{ row.originName }} </template>

            <template #shippedQuantity="{ row }">
              <Input v-model:value="row.shippedQuantity" placeholder="本次发运重量" />
            </template>

            <template #sourceDocumentItemNumber="{ row }">
              {{ row.sourceDocumentItemNumber }}
            </template>

            <template #sourceDocumentName="{ row }">
              {{ row.sourceDocumentName }}
            </template>

            <template #sourceDocumentCode="{ row }">
              {{ row.sourceDocumentCode }}
            </template>

            <template #remarks="{ row }">
              <Input v-model:value="row.remarks" placeholder="备注" />
            </template>
          </GridGoods>
        </div>

        <!-- 附件信息 -->
        <BaseAttachmentList
          v-model="detailForm.attachmentList"
          :business-id="detailForm.id"
          business-type="SCM_SHIPMENT"
          edit-mode
        />
      </Form>
    </div>
  </BasicPopup>
</template>

<style scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.fe-basic-caption-border) {
  display: none;
}
</style>
