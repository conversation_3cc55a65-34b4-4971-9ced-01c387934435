<script setup lang="ts">
import type { PriceProductInfo } from '#/api/price-monitor';

import { ref } from 'vue';

import { Modal } from 'ant-design-vue';

const emit = defineEmits(['submit']);
const openRef = ref(false);
const formData = ref<PriceProductInfo>({});
const formRef = ref();
const handleOk = async () => {
  await formRef.value.validate();
  delete formData.value.id;
  emit('submit', formData.value);
  openRef.value = false;
};
const showModal = (row: PriceProductInfo) => {
  formData.value = {
    ...row,
    price: '',
    priceDate: '',
  };
  delete formData.value.createTime;
  openRef.value = true;
};
const handleCancel = () => {
  formRef.value.resetFields();
};
const formRules = {
  price: [{ required: true, message: '请输入价格' }],
  priceDate: [{ required: true, message: '请选择日期' }],
};
defineExpose({
  showModal,
});
</script>
<template>
  <Modal
    v-model:open="openRef"
    title="新增价格"
    @ok="handleOk"
    @cancel="handleCancel"
    :body-style="{ padding: '10px 24px' }"
  >
    <a-form :model="formData" :rules="formRules" ref="formRef">
      <a-form-item label="分类名称" name="categoryName">
        {{ formData.categoryName }}
      </a-form-item>
      <a-form-item label="商品名称" name="productName">
        {{ formData.productName }}
      </a-form-item>
      <a-form-item label="价格" name="price">
        <a-input-number v-model:value="formData.price" class="w-full" />
      </a-form-item>
      <a-form-item label="日期" name="priceDate">
        <a-date-picker v-model:value="formData.priceDate" value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
      </a-form-item>
    </a-form>
  </Modal>
</template>
<style lang="less" scoped></style>
