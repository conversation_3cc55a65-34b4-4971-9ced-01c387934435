<script lang="ts" setup>
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PriceProductInfo } from '#/api/price-monitor';

import { ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPriceDetailApi } from '#/api/price-monitor';

defineEmits(['register']);
const params = ref();
const init = async (data: PriceProductInfo) => {
  params.value = data;
};
const [registerPopup] = usePopupInner(init);
const gridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  columns: [
    { field: 'priceDate', title: '日期', formatter: 'formatDate' },
    {
      field: 'productName',
      title: '产品名称',
    },
    {
      field: 'price',
      title: '价格（元/吨）',
    },
  ],
  // height: 'auto',
  proxyConfig: {
    ajax: {
      query: async () => {
        return await getPriceDetailApi(params.value.productId);
      },
    },
  },
};
const [Grid] = useVbenVxeGrid({
  gridOptions,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" title="价格明细" @register="registerPopup">
    <Grid />
  </BasicPopup>
</template>
