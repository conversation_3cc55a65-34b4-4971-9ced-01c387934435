<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrderInfo } from '#/api';

import { ref, watch } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { formatDate, formatMoney } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { BusinessStructureEnum, infoSalesListApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['register', 'ok']);
const orderDetail = ref<Partial<OrderInfo>>({});
const init = async (data: OrderInfo & { pageType: string }) => {
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_sales_order', businessKey: data.id });
  if (data?.id) {
    orderDetail.value = await infoSalesListApi({ id: data.id });
  }
};

const { useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading, isProcessInstance } =
  useWorkflowBase();
const pageType = ref('detail');
const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const [registerPopup, { closePopup }] = usePopupInner(init);

const baseGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  border: 'inner',
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const columns = [
  { field: 'total' },
  { field: 'itemNumber', title: '行号', minWidth: '150px' },
  // { field: 'categoryName', title: '商品分类' },
  { field: 'productName', title: '商品名称', minWidth: '150px' },
  { field: 'productCode', title: '商品编码', minWidth: '150px' },
  { field: 'productAlias', title: '商品别名', minWidth: '150px' },
  { field: 'specifications', title: '规格型号', minWidth: '150px' },
  { field: 'measureUnit', title: '单位', minWidth: '150px' },
  // { field: 'brandName', title: '牌号/材质', minWidth: '150px' },
  // { field: 'originName', title: '产地/厂商', minWidth: '150px' },
  { field: 'quantity', title: '重量', minWidth: '150px' },
  {
    field: 'priceWithTax',
    title: '含税单价',
    minWidth: '150px',
    formatter: 'formatMoney',
  },
  // {
  //   field: 'priceWithoutTax',
  //   title: '不含税单价',
  //   minWidth: '150px',
  //   formatter: 'formatMoney',
  // },
  { field: 'taxRate', title: '税率(%)', minWidth: '150px' },
  {
    field: 'amountWithTax',
    title: '含税金额',
    minWidth: '150px',
    formatter: 'formatMoney',
    footerFormatter({ itemValue }: { itemValue: number }) {
      return formatMoney(itemValue);
    },
  },
  {
    field: 'taxAmount',
    title: '税额',
    minWidth: '150px',
    formatter: 'formatMoney',
    footerFormatter({ itemValue }: { itemValue: number }) {
      return formatMoney(itemValue);
    },
  },
  // {
  //   field: 'amountWithoutTax',
  //   title: '不含税金额',
  //   minWidth: '150px',
  //   formatter: 'formatMoney',
  // },
  { field: 'sourceItemNumber', title: '采购行号', minWidth: '150px', slots: { default: 'sourceItemNumber' } },
  { field: 'sourcePrice', title: '采购含税单价', minWidth: '150px', formatter: 'formatMoney' },
  { field: 'remarks', title: '备注', minWidth: '200px' },
];
const bankGridOptions = {
  showFooter: true,
  footerMethod() {
    const footerRow = {
      total: '合计',
      amountWithTax: orderDetail.value.totalAmountWithTax,
      taxAmount: orderDetail.value.totalTaxAmount,
      amountWithoutTax: orderDetail.value.totalAmountWithoutTax,
    };
    return [footerRow];
  },
  columns,
  ...baseGridOptions,
} as VxeTableGridOptions;
const [ProductGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
watch(
  () => orderDetail.value,
  (val = {}) => {
    bankGridApi.grid.reloadData(val.salesOrderItem ?? []);
    bankGridOptions.columns =
      val.businessStructure === BusinessStructureEnum.GENERAL
        ? columns.filter((item) => item.field !== 'sourceLine' && item.field !== 'sourcePrice')
        : columns;
    bankGridApi.grid.reloadColumn(bankGridOptions.columns);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="订单信息" @register="registerPopup">
    <template #insertToolbar>
      <ViewButton v-if="isWorkflow" />
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="销售订单编号">
          {{ orderDetail.salesOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="销售订单名称">
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目编号">
          {{ orderDetail.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ orderDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="业务结构">
          <StatusTag code="BUS_STRUCTURE" :value="orderDetail.businessStructure" />
        </a-descriptions-item>
        <a-descriptions-item label="项目模式">
          <StatusTag code="PROJECT_MODE" :value="orderDetail.projectModel" />
        </a-descriptions-item>
        <a-descriptions-item label="采购订单编号" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">
          {{ orderDetail.salesOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="关联采购订单" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="下游企业">
          {{ orderDetail.purchaserCompanyName }}
        </a-descriptions-item>
        <!--<a-descriptions-item label="业务负责人">-->
        <!--  {{ orderDetail.businessManagerName }}-->
        <!--</a-descriptions-item>-->
        <!--<a-descriptions-item label="运营负责人">-->
        <!--  {{ orderDetail.operationManagerName }}-->
        <!--</a-descriptions-item>-->
        <a-descriptions-item label="业务日期">
          {{ formatDate(orderDetail.businessDate || '') }}
        </a-descriptions-item>
        <a-descriptions-item label="预计结束日期">
          {{ formatDate(orderDetail.estimatedEndDate || '') }}
        </a-descriptions-item>
        <!--<a-descriptions-item-->
        <!--  label="预付款比例(%)"-->
        <!--  v-if="orderDetail.businessStructure === BusinessStructureEnum.PURCHASE"-->
        <!--&gt;-->
        <!--  {{ formatMoney(orderDetail.prepaymentRatio) }}-->
        <!--</a-descriptions-item>-->
        <a-descriptions-item label="预付款金额" v-if="orderDetail.businessStructure === BusinessStructureEnum.PURCHASE">
          {{ formatMoney(orderDetail.prepaymentAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="保证金金额" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">
          {{ formatMoney(orderDetail.depositAmount) }}
        </a-descriptions-item>
        <!--<a-descriptions-item label="垫资比例(%)" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">-->
        <!--  {{ formatMoney(orderDetail.advanceRatio) }}-->
        <!--</a-descriptions-item>-->
        <!--<a-descriptions-item label="账期（天)" v-if="orderDetail.businessStructure === BusinessStructureEnum.SALE">-->
        <!--  {{ orderDetail.paymentTermDays }}-->
        <!--</a-descriptions-item>-->
        <a-descriptions-item label="任务类型">
          <StatusTag code="TASK_TYPE" :value="orderDetail.taskType" :option="{ dictValueType: 'number' }" />
        </a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ orderDetail.remarks }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="商品信息" />
      <ProductGrid>
        <template #sourceItemNumber="{ row }">
          {{ row.sourceOrderCode }}{{ row.sourceItemNumber ? `-${row.sourceItemNumber}` : '' }}
        </template>
      </ProductGrid>
      <BaseAttachmentList border="inner" :business-id="orderDetail.id" business-type="SCM_SALES_ORDER" />
    </div>
  </BasicPopup>
</template>

<style></style>
