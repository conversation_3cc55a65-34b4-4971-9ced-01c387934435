import { calculateTaxAmount, dividedByFun, formatFun, minusFun, plusFun, timesFun } from '#/utils/calculate';

export const baseGridOptions = {
  showOverflow: 'title',
  keepSource: true,
  showFooter: true,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

export interface BatchSettingData {
  inputValue: number;
  radioType: number;
  segmentedValue?: string;
}
interface ProductRow {
  taxRate?: number | string;
  priceWithoutTax?: number | string;
  priceWithTax?: number | string;
  quantity?: number | string;
  returnQuantity?: number | string;
  amountWithTax?: number | string;
  taxAmount?: number | string;
  amountWithoutTax?: number | string;
  [key: string]: any;
}

/**
 * 计算行数据
 *
 * 含税金额= 数量 * 含税单价
   税额 = 含税金额 -（含税金额 /（1+税率)）
   不含税金额 = 含税金额 - 税额
   不含税单价 = 不含税金额 / 数量

   不含税金额 = 不含税单价 * 数量
   含税金额 = 不含税金额  * （1+税率）
   税额 = 含税金额 - 不含税金额
   含税单价 = 含税金额 / 数量

 * @param row 行数据
 * @param useTaxPriceCalc 是否使用不含税价格计算
 * @param gridApi 表格API实例（可选）
 */
export function numCalculateRow(row: ProductRow, useTaxPriceCalc?: boolean, gridApi?: any) {
  const quantity = row.quantity || 0; // 数量
  const taxRate = dividedByFun(row.taxRate || 0, 100); // 税率
  if (useTaxPriceCalc) {
    const priceWithoutTax = row.priceWithoutTax || 0; // 不含税单价

    // 不含税金额
    const amountWithoutTax = timesFun(priceWithoutTax, quantity);
    // 含税金额
    const amountWithTax = timesFun(amountWithoutTax, plusFun(1, taxRate));
    // 税额
    const taxAmount = minusFun(amountWithTax, amountWithoutTax);
    // 含税单价
    const priceWithTax = dividedByFun(amountWithTax, quantity);
    // 最终展示值
    row.amountWithoutTax = formatFun(amountWithoutTax);
    row.amountWithTax = formatFun(amountWithTax);
    row.taxAmount = formatFun(taxAmount);
    row.priceWithTax = formatFun(priceWithTax);
  } else {
    const priceWithTax = row.priceWithTax || 0; // 含税单价

    // 含税金额
    const amountWithTax = timesFun(quantity, priceWithTax);
    // 税额
    const taxAmount = calculateTaxAmount(amountWithTax, taxRate);
    // 不含税金额
    const amountWithoutTax = minusFun(amountWithTax, taxAmount);
    // 不含税单价
    const priceWithoutTax = dividedByFun(amountWithoutTax, quantity);
    // 最终展示值
    row.amountWithTax = formatFun(amountWithTax);
    row.taxAmount = formatFun(taxAmount);
    row.amountWithoutTax = formatFun(amountWithoutTax);
    row.priceWithoutTax = formatFun(priceWithoutTax);
  }
  if (gridApi?.grid) {
    gridApi.grid.updateFooter();
  }
}

/**
 * 批量设置计算函数
 * @param data 批量设置参数
 * @param row 行数据
 * @param useTaxPriceCalc 是否使用不含税价格计算
 */
export function batchSettingCalculate(data: BatchSettingData, row: ProductRow, useTaxPriceCalc: boolean): ProductRow {
  const updatedRow = { ...row };

  if (data.segmentedValue === '调整税率') {
    // 处理调整税率逻辑
    switch (data.radioType) {
      case 1: {
        updatedRow.taxRate = data.inputValue;
        break;
      }
      case 3: {
        updatedRow.taxRate = plusFun(updatedRow.taxRate || 0, data.inputValue);
        break;
      }
    }
  } else {
    if (useTaxPriceCalc) {
      // 按不含税单价计算模式
      switch (data.radioType) {
        case 1: {
          updatedRow.priceWithoutTax = data.inputValue;
          break;
        }
        case 2: {
          updatedRow.priceWithoutTax = plusFun(
            updatedRow.priceWithoutTax || 0,
            timesFun(updatedRow.priceWithoutTax || 0, dividedByFun(data.inputValue, 100)),
          );
          break;
        }
        case 3: {
          updatedRow.priceWithoutTax = plusFun(updatedRow.priceWithoutTax || 0, data.inputValue);
          break;
        }
      }
    } else {
      // 按含税单价计算模式
      switch (data.radioType) {
        case 1: {
          updatedRow.priceWithTax = data.inputValue;
          break;
        }
        case 2: {
          updatedRow.priceWithTax = plusFun(
            updatedRow.priceWithTax || 0,
            timesFun(updatedRow.priceWithTax || 0, dividedByFun(data.inputValue, 100)),
          );
          break;
        }
        case 3: {
          updatedRow.priceWithTax = plusFun(updatedRow.priceWithTax || 0, data.inputValue);
          break;
        }
      }
    }
  }

  return updatedRow;
}

/**
 * 批量设置确认函数
 * @param data 批量设置参数
 * @param gridApi 表格API实例
 * @param productList 产品列表
 * @param useTaxPriceCalc 是否使用不含税价格计算
 */
export function numBatchSettingConfirm(
  data: BatchSettingData,
  gridApi: any,
  productList: any,
  useTaxPriceCalc: boolean,
) {
  const $grid = gridApi.grid;
  const selectRecords = $grid.getCheckboxRecords();
  const visibleData = $grid.getTableData().visibleData;
  const updatedData = visibleData.map((row: any) => {
    if (selectRecords.includes(row)) {
      const updatedRow = batchSettingCalculate(data, row, useTaxPriceCalc);
      // 触发行计算
      numCalculateRow(updatedRow, useTaxPriceCalc, gridApi);
      return updatedRow;
    }
    return row;
  });

  // 更新数据源并刷新表格
  productList.value = updatedData;
  $grid.reloadData(updatedData);
}
