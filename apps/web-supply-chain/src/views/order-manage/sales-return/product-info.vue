<script setup lang="ts">
import type { BatchSettingData } from '../components/comm';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderInfo, OrderProductInfo } from '#/api';

import { ref } from 'vue';

import { getCombinedErrorMessagesString } from '@vben/utils';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getSalesOrderItemListApi } from '#/api';
import { formatFun, plusFun } from '#/utils/calculate';
import ProductPick from '#/views/order-manage/components/product-pick.vue';

import BatchSettingModal from '../components/batchSettingModal.vue';
import { baseGridOptions, numBatchSettingConfirm, numCalculateRow } from '../components/comm';

const props = defineProps<{ pageType: string; propsData: OrderInfo }>();
defineEmits(['emitReset']);
const productList = ref<OrderProductInfo[]>([]);
const batchSettingModal = ref();
// 新增初始化方法
const setProductData = (data: OrderProductInfo[]) => {
  const formattedData = data.map((item) => {
    return {
      ...item,
      amountWithTax: formatFun(item.amountWithTax),
      taxAmount: formatFun(item.taxAmount),
      amountWithoutTax: formatFun(item.amountWithoutTax),
      priceWithTax: formatFun(item.priceWithTax),
      priceWithoutTax: formatFun(item.priceWithoutTax),
    };
  });
  productList.value = formattedData;
  if (gridApi.grid) {
    gridApi.grid.reloadData(productList.value);
  }
};
// 表格配置
const gridOptions = {
  ...baseGridOptions,
  border: 'inner',
  data: productList.value,
  editRules: {
    returnQuantity: [
      { required: true, message: '请输入退货重量' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确重量,最多4位小数' },
    ],
    taxRate: [
      { required: true, message: '请输入税率' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确税率,最多4位小数' },
    ],
    priceWithTax: [
      { required: true, message: '请输入含税单价' },
      // { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确含税单价,最多2位小数' },
    ],
    priceWithoutTax: [
      { required: true, message: '请输入不含税单价' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确不含税单价,最多2位小数' },
    ],
  },
  columns: [
    { field: 'checkbox', type: 'checkbox', width: '50px', fixed: 'left' },
    // {
    //   field: 'categoryName',
    //   title: '商品分类',
    // },
    {
      field: 'productName',
      title: '商品名称',
      slots: { edit: 'edit_productName' },
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { edit: 'edit_spuCode' },
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { edit: 'edit_productAlias' },
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { edit: 'edit_skuName' },
    },
    {
      field: 'measureUnit',
      title: '单位',
      slots: { edit: 'edit_measureUnit' },
    },
    // {
    //   field: 'brandName',
    //   title: '牌号/材质',
    //   slots: { edit: 'edit_brandName' },
    // },
    // {
    //   field: 'originName',
    //   title: '产地/厂商',
    //   slots: { edit: 'edit_originName' },
    // },
    {
      field: 'originalQuantity',
      title: '数量',
      slots: { edit: 'edit_originalQuantity' },
    },
    {
      field: 'returnQuantity',
      title: '退货数量',
      slots: { default: 'edit_returnQuantity' },
    },
    {
      field: 'priceWithTax',
      title: '含税单价',
      slots: { default: 'edit_priceWithTax' },
      width: '150px',
    },
    {
      field: 'taxRate',
      title: '税率(%)',
      slots: { default: 'edit_taxRate' },
      width: '150px',
    },
    {
      field: 'amountWithTax',
      title: '含税金额',
      slots: { edit: 'edit_amountWithTax' },
      width: '150px',
    },
    {
      field: 'taxAmount',
      title: '税额',
      slots: { edit: 'edit_taxAmount' },
      width: '150px',
    },
    {
      field: 'amountWithoutTax',
      title: '不含税金额',
      slots: { edit: 'edit_amountWithoutTax' },
      width: '150px',
    },
    // {
    //   field: 'priceWithoutTax',
    //   title: '不含税单价',
    //   slots: { default: 'edit_priceWithoutTax' },
    //   width: '150px',
    // },
    {
      field: 'sourceItemNumber',
      title: '采购订单行号',
      width: '150px',
      slots: { default: 'sourceItemNumber' },
    },
    {
      field: 'remarks',
      title: '备注',
      slots: { default: 'edit_remarks' },
      minWidth: '160px',
    },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax: formatFun(amountWithTax),
      taxAmount: formatFun(taxAmount),
      amountWithoutTax: formatFun(amountWithoutTax),
    };
    return [footerRow];
  },
} as VxeTableGridOptions;

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
// 删除行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      await $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  async getProductData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const errMap = await $grid.validate(true);
      if (errMap) {
        const errMessage = getCombinedErrorMessagesString(errMap);
        if (errMessage) {
          message.error(errMessage);
          return null;
        }
      }
      const { visibleData } = gridApi.grid.getTableData();
      const processedData = visibleData.map((item, index) => ({
        ...item,
        salesOrderItemNumber: item.itemNumber,
        itemNumber: index + 1,
      }));
      return processedData;
    } else {
      return [];
    }
  },
  setProductData,
});
const useTaxPriceCalc = ref(false); // 控制计算方式

const calculateRow = (row: OrderProductInfo) => {
  row.quantity = row.returnQuantity;
  numCalculateRow(row, useTaxPriceCalc.value, gridApi);
};
const batchSettingConfirm = (data: BatchSettingData) => {
  numBatchSettingConfirm(data, gridApi, productList, useTaxPriceCalc.value);
};
// 批量设置
const batchSetting = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      batchSettingModal.value.openModal(useTaxPriceCalc.value);
    } else {
      message.warning('请选择数据');
    }
  }
};
const ProductPickRef = ref();
// 重置
const resetTableData = async () => {
  if (!props.propsData.salesOrderId) {
    return message.warning('请先选择销售订单');
  }
  const includeNumbers = productList.value.map((item: any) => item.sourceItemNumber);
  const itemRecord = await getSalesOrderItemListApi({
    orderId: props.propsData.salesOrderId,
    type: 'ALL',
    includeNumbers,
  });
  const hasSourceItemNumber = cloneDeep(
    gridApi.grid.getTableData().visibleData.map((item: any) => item.sourceItemNumber),
  );
  console.log('hasSourceItemNumber1', hasSourceItemNumber);
  const goods = await ProductPickRef.value.pick(itemRecord, hasSourceItemNumber);
  const newGoods = goods.filter((item: any) => !hasSourceItemNumber.includes(item.sourceItemNumber));
  console.log('hasSourceItemNumber2', hasSourceItemNumber);
  console.log('newGoods', newGoods);
  newGoods.forEach((item: any) => {
    item.originalQuantity = item.quantity;
  });
  await gridApi.grid.insertAt(newGoods, -1);
};
</script>

<template>
  <div>
    <Grid>
      <template #toolbar-actions>
        <!--<a-checkbox class="mr-2" @change="(e: any) => (useTaxPriceCalc = e.target.checked)">-->
        <!--  按不含税单价计算-->
        <!--</a-checkbox>-->
      </template>
      <template #toolbarTools>
        <a-space>
          <!--<a-button-->
          <!--  v-if="props.pageType !== 'audit'"-->
          <!--  class="mr-2"-->
          <!--  type="primary"-->
          <!--  @click="exportProductApi(productList, 'sales/return')"-->
          <!--&gt;-->
          <!--  导出商品-->
          <!--</a-button>-->
          <a-button v-if="props.pageType !== 'audit'" class="mr-2" @click="batchSetting"> 批量设置 </a-button>
          <a-button v-if="props.pageType !== 'audit'" type="primary" class="mr-2" @click="resetTableData">
            选择商品
          </a-button>
          <a-button v-if="props.pageType !== 'audit'" class="mr-2" danger @click="removeAccount">删行</a-button>
        </a-space>
      </template>
      <template #edit_productName="{ row }">
        <a-input v-model:value="row.productName" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_productAlias="{ row }">
        <a-input v-model:value="row.productAlias" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_skuName="{ row }">
        <a-input v-model:value="row.specifications" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_spuCode="{ row }">
        <a-input v-model:value="row.productCode" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_measureUnit="{ row }">
        <a-input v-model:value="row.measureUnit" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_brandName="{ row }">
        <a-input v-model:value="row.brandName" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_originName="{ row }">
        <a-input v-model:value="row.originName" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_originalQuantity="{ row }">
        <a-input-number v-model:value="row.originalQuantity" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_returnQuantity="{ row }">
        <a-input-number
          v-model:value="row.returnQuantity"
          placeholder="请输入重量"
          @change="calculateRow(row)"
          :precision="4"
          :disabled="props.pageType === 'audit'"
        />
      </template>
      <template #edit_priceWithTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithTax"
          placeholder="请输入含税单价"
          @change="calculateRow(row)"
          :precision="2"
          :disabled="useTaxPriceCalc || props.pageType === 'audit'"
        />
      </template>
      <template #edit_taxRate="{ row }">
        <a-input-number
          v-model:value="row.taxRate"
          placeholder="请输入税率"
          @change="calculateRow(row)"
          :precision="4"
          :disabled="props.pageType === 'audit'"
        />
      </template>
      <template #edit_amountWithTax="{ row }">
        <a-input-number v-model:value="row.amountWithTax" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_taxAmount="{ row }">
        <a-input-number v-model:value="row.taxAmount" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_amountWithoutTax="{ row }">
        <a-input-number v-model:value="row.amountWithoutTax" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_priceWithoutTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithoutTax"
          placeholder="请输入不含税单价"
          @change="calculateRow(row)"
          :precision="2"
          :disabled="!useTaxPriceCalc || props.pageType === 'audit'"
        />
      </template>
      <template #sourceItemNumber="{ row }"> {{ row.sourceOrderCode }}-{{ row.sourceItemNumber }} </template>
      <template #edit_remarks="{ row }">
        <a-input v-model:value="row.remarks" placeholder="请输入备注" :disabled="props.pageType === 'audit'" />
      </template>
    </Grid>
    <ProductPick ref="ProductPickRef" />
    <BatchSettingModal ref="batchSettingModal" @confirm="batchSettingConfirm" />
  </div>
</template>

<style scoped></style>
