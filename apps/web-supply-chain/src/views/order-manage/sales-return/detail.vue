<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrderInfo } from '#/api';

import { ref, watch } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { formatMoney } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { infoSalesReturnListApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import { plusFun } from '#/utils/calculate';

const emit = defineEmits(['register', 'ok']);

const orderDetail = ref<Partial<OrderInfo>>({});

const { useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading, isProcessInstance } =
  useWorkflowBase();
const pageType = ref('detail');
const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const init = async (data: OrderInfo & { pageType: string }) => {
  pageType.value = data.pageType;
  await initWorkflow({ formKey: 'scm_sales_return_order', businessKey: data.id });
  if (data?.id) {
    orderDetail.value = await infoSalesReturnListApi({ id: data.id });
  }
};
const [registerPopup, { closePopup }] = usePopupInner(init);

const baseGridOptions = {
  showFooter: true,
  pagerConfig: {
    enabled: false,
  },
  border: 'inner',
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const bankGridOptions = {
  columns: [
    { field: 'checkbox', width: '50px', fixed: 'left' },
    // {
    //   field: 'categoryName',
    //   title: '商品分类',
    // },
    { field: 'productName', title: '商品名称', minWidth: '160px' },
    { field: 'productCode', title: '商品编码', minWidth: '160px' },
    { field: 'productAlias', title: '商品别名', minWidth: '160px' },
    { field: 'specifications', title: '规格型号', minWidth: '160px' },
    { field: 'measureUnit', title: '单位', minWidth: '160px' },
    // { field: 'brandName', title: '牌号/材质', minWidth: '160px' },
    // { field: 'originName', title: '产地/厂商', minWidth: '160px' },
    { field: 'returnQuantity', title: '退货重量', minWidth: '160px' },
    { field: 'priceWithTax', title: '含税单价', minWidth: '160px', formatter: 'formatMoney' },
    { field: 'taxRate', title: '税率(%)', minWidth: '160px' },
    {
      field: 'amountWithTax',
      title: '含税金额',
      minWidth: '160px',
      formatter: 'formatMoney',
      footerFormatter({ itemValue }: { itemValue: number }) {
        return formatMoney(itemValue);
      },
    },
    {
      field: 'taxAmount',
      title: '税额',
      minWidth: '160px',
      formatter: 'formatMoney',
      footerFormatter({ itemValue }: { itemValue: number }) {
        return formatMoney(itemValue);
      },
    },
    {
      field: 'amountWithoutTax',
      title: '不含税金额',
      minWidth: '160px',
      formatter: 'formatMoney',
      footerFormatter({ itemValue }: { itemValue: number }) {
        return formatMoney(itemValue);
      },
    },
    // {
    //   field: 'priceWithoutTax',
    //   title: '不含税单价',
    //   minWidth: '160px',
    //   formatter: ({ cellValue }) => formatFun(cellValue),
    // },
    { field: 'itemNumber', title: '采购订单行号', minWidth: '160px', slots: { default: 'sourceItemNumber' } },
    { field: 'remarks', title: '备注', minWidth: '160px' },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax,
      taxAmount,
      amountWithoutTax,
    };
    return [footerRow];
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [ProducGrid, productGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
watch(
  () => orderDetail.value,
  (val = {}) => {
    productGridApi.grid.reloadData(val.salesReturnOrderItem ?? []);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="订单信息" @register="registerPopup">
    <template #insertToolbar>
      <ViewButton v-if="isWorkflow" />
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
      </div>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="销售退货单编号">
          {{ orderDetail.salesReturnOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="销售退货单名称">
          {{ orderDetail.salesReturnOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目编号">
          {{ orderDetail.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ orderDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="订单编号">
          {{ orderDetail.salesOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="订单名称">
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="下游企业">
          {{ orderDetail.purchaserCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="业务日期">
          {{ orderDetail.businessDate }}
        </a-descriptions-item>
        <!--<a-descriptions-item label="预计结束日期">-->
        <!--  {{ orderDetail.estimatedEndDate }}-->
        <!--</a-descriptions-item>-->
        <a-descriptions-item label="任务类型">
          <StatusTag code="TASK_TYPE" :value="orderDetail.taskType" :option="{ dictValueType: 'number' }" />
        </a-descriptions-item>
        <!--<a-descriptions-item label="创建时间">-->
        <!--  {{ orderDetail.createTime }}-->
        <!--</a-descriptions-item>-->
        <!--<a-descriptions-item label="最后修改时间">-->
        <!--  {{ orderDetail.updateTime }}-->
        <!--</a-descriptions-item>-->
        <!-- <a-descriptions-item label="业务负责人">
          {{ orderDetail.businessManagerName }}
        </a-descriptions-item> -->
        <a-descriptions-item label="生效时间">
          {{ orderDetail.updateTime }}
        </a-descriptions-item>
        <a-descriptions-item label="审批状态">
          <StatusTag code="REVIEW_STATUS" :value="orderDetail.approvalStatus" />
        </a-descriptions-item>
        <a-descriptions-item label="业务状态">
          <StatusTag code="BUS_STATUS" :value="orderDetail.status" />
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="商品信息" />
      <ProducGrid>
        <template #sourceItemNumber="{ row }">
          {{ row.sourceOrderCode }}{{ row.sourceItemNumber ? `-${row.sourceItemNumber}` : '' }}
        </template>
      </ProducGrid>
      <BaseAttachmentList border="inner" :business-id="orderDetail.id" business-type="SCM_SALESRETURN_ORDER" />
    </div>
  </BasicPopup>
</template>

<style></style>
