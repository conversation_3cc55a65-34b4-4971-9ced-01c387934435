<script setup lang="ts">
import type { VxeTableDefines } from 'vxe-table';

import type { BatchSettingData } from '../components/comm';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderInfo, OrderProductInfo } from '#/api';

import { ref, watch } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { getCombinedErrorMessagesString } from '@vben/utils';

import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { BusinessStructureEnum, getSalesOrderItemListApi } from '#/api';
import { formatFun, plusFun } from '#/utils/calculate';
import ProductPick from '#/views/order-manage/components/product-pick.vue';

import BatchSettingModal from '../components/batchSettingModal.vue';
import { baseGridOptions, numBatchSettingConfirm, numCalculateRow } from '../components/comm';
import ProductPopup from '../components/product-popup.vue';

const props = defineProps<{
  formProps: OrderInfo;
  pageType: string;
}>();
defineEmits(['orderInfoFun']);
const productList = ref<OrderProductInfo[]>([]);
const batchSettingModal = ref();
const useTaxPriceCalc = ref(false); // 控制计算方式
// 新增初始化方法
const setProductData = (data: OrderProductInfo[]) => {
  productList.value = data.map((item) => {
    return {
      ...item,
    };
  });
  if (gridApi.grid) {
    gridApi.grid.reloadData(productList.value);
  }
};

// const importTemplateData = computed<TemplateData>(() => {
//   return {
//     title: '商品',
//     startRow: 2,
//     tableField: [
//       { header: '商品名称', key: 'productName' },
//       { header: '商品编码', key: 'productCode' },
//       { header: '商品别名', key: 'productAlias' },
//       { header: '规格型号', key: 'specifications' },
//       { header: '单位', key: 'measureUnit' },
//       { header: '重量', key: 'quantity' },
//       { header: '含税单价', key: 'priceWithTax', disable: useTaxPriceCalc.value },
//       { header: '不含税单价', key: 'priceWithoutTax', disable: !useTaxPriceCalc.value },
//       { header: '税率(%)', key: 'taxRate' },
//       { header: '备注', key: 'remarks' },
//     ],
//   };
// });

const columns = [
  { field: 'checkbox', type: 'checkbox', width: '50px', fixed: 'left' },
  {
    field: 'itemNumber',
    title: '行号',
    type: 'seq',
  },
  // {
  //   field: 'categoryName',
  //   title: '商品分类',
  // },
  {
    field: 'productName',
    title: '商品名称',
    slots: { edit: 'edit_productName' },
    minWidth: '150px',
  },
  {
    field: 'productCode',
    title: '商品编码',
    slots: { edit: 'edit_spuCode' },
    width: '150px',
  },
  {
    field: 'productAlias',
    title: '商品别名',
    slots: { default: 'edit_productAlias' },
    width: '150px',
  },
  {
    field: 'specifications',
    title: '规格型号',
    slots: { edit: 'edit_skuName' },
    width: '150px',
  },
  {
    field: 'measureUnit',
    title: '单位',
    slots: { edit: 'edit_measureUnit' },
  },
  // {
  //   field: 'brandName',
  //   title: '牌号/材质',
  //   slots: { edit: 'edit_brandName' },
  // },
  // {
  //   field: 'originName',
  //   title: '产地/厂商',
  //   slots: { edit: 'edit_originName' },
  // },
  {
    field: 'quantity',
    title: '数量',
    slots: { default: 'edit_quantity' },
    width: '150px',
  },
  {
    field: 'priceWithTax',
    title: '含税单价',
    slots: { default: 'edit_priceWithTax' },
    width: '150px',
  },
  // {
  //   field: 'priceWithoutTax',
  //   title: '不含税单价',
  //   slots: { default: 'edit_priceWithoutTax' },
  //   width: '150px',
  // },
  {
    field: 'taxRate',
    title: '税率(%)',
    slots: { default: 'edit_taxRate' },
    width: '100px',
  },
  {
    field: 'amountWithTax',
    title: '含税金额',
    slots: { edit: 'edit_amountWithTax' },
    formatter: 'formatMoney',
    width: '150px',
  },
  {
    field: 'amountWithoutTax',
    title: '不含税金额',
    slots: { edit: 'edit_amountWithoutTax' },
    formatter: 'formatMoney',
    width: '150px',
  },
  {
    field: 'taxAmount',
    title: '税额',
    slots: { edit: 'edit_taxAmount' },
    formatter: 'formatMoney',
    width: '150px',
  },
  {
    field: 'sourceItemNumber',
    title: '销售行号',
    minWidth: '150px',
    slots: { default: 'sourceItemNumber' },
  },
  {
    field: 'sourcePrice',
    title: '销售含税单价',
    width: '150px',
    slots: { default: 'edit_sourcePrice' },
  },
  {
    field: 'remarks',
    title: '备注',
    slots: { default: 'edit_remarks' },
    minWidth: '200px',
  },
] as VxeTableDefines.ColumnOptions[];

// 表格配置
const gridOptions = {
  ...baseGridOptions,
  border: 'inner',
  data: productList.value,
  editRules: {
    quantity: [
      { required: true, message: '请输入采购重量' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确重量,最多4位小数' },
    ],
    taxRate: [
      { required: true, message: '请输入税率' },
      { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入正确税率,最多4位小数' },
    ],
    priceWithTax: [
      { required: true, message: '请输入含税单价' },
      // { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确含税单价,最多2位小数' },
    ],
    priceWithoutTax: [
      { required: true, message: '请输入不含税单价' },
      { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确不含税单价,最多2位小数' },
    ],
  },
  columns,
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax: formatFun(amountWithTax),
      taxAmount: formatFun(taxAmount),
      amountWithoutTax: formatFun(amountWithoutTax),
    };
    return [footerRow];
  },
} as VxeTableGridOptions;

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

watch(
  () => props.formProps.businessStructure,
  (newValue) => {
    gridOptions.columns =
      newValue === BusinessStructureEnum.GENERAL
        ? columns.filter((item) => item.field !== 'sourceItemNumber' && item.field !== 'sourcePrice')
        : columns;
    gridApi.setGridOptions(gridOptions);
  },
);

// 选择商品弹窗关闭时获取返回的数据
const [Modal, modalApi] = useVbenModal({
  connectedComponent: ProductPopup,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      const selectedProducts = modalApi.getData() || [];
      selectedProducts.forEach((item: any) => {
        const goods = cloneDeep(item);
        item.priceWithTax = goods.sourcePrice;
        item.sourcePrice = goods.priceWithTax;
        item.taxRate = 13;
      });
      gridApi.grid.insertAt(selectedProducts, -1);
    }
  },
});
// 删除行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  async getProductData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const errMap = await $grid.validate(true);
      if (errMap) {
        const errMessage = getCombinedErrorMessagesString(errMap);
        if (errMessage) {
          message.error(errMessage);
          return null;
        }
      }
      const { visibleData } = gridApi.grid.getTableData();
      const processedData = visibleData.map((item, index) => ({
        ...item,
        itemNumber: index + 1,
      }));
      const footerData: any = $grid.getTableData().footerData?.[0];
      return {
        items: processedData,
        totalAmountWithTax: footerData.amountWithTax,
        totalTaxAmount: footerData.taxAmount,
        totalAmountWithoutTax: footerData.amountWithoutTax,
      };
    }
  },
  setProductData,
});
// const importSuccess = (data: any) => {
//   const $grid = gridApi.grid;
//   if ($grid) {
//     $grid.reloadData(data);
//   }
// };
// 批量设置
const batchSetting = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      batchSettingModal.value.openModal(useTaxPriceCalc.value);
    } else {
      message.warning('请选择数据');
    }
  }
};
const calculateRow = (row: OrderProductInfo) => {
  numCalculateRow(row, useTaxPriceCalc.value, gridApi);
};
const batchSettingConfirm = (data: BatchSettingData) => {
  numBatchSettingConfirm(data, gridApi, productList, useTaxPriceCalc.value);
};
const ProductPickRef = ref();
// 重置
const resetTableData = async () => {
  if (!props.formProps.salesOrderId) {
    return message.warning('请先选择销售订单');
  }
  const includeNumbers = productList.value.map((item: any) => item.sourceItemNumber);
  const itemRecord = await getSalesOrderItemListApi({
    orderId: props.formProps.salesOrderId,
    type: 'UNSPLIT',
    includeNumbers,
  });
  const hasSourceItemNumber = gridApi.grid.getTableData().visibleData.map((item: any) => item.sourceItemNumber);
  const goods = await ProductPickRef.value.pick(itemRecord, hasSourceItemNumber);
  const newGoods = goods.filter((item: any) => !hasSourceItemNumber.includes(item.sourceItemNumber));
  newGoods.forEach((item: any) => {
    item.priceWithoutTax = item.sourcePrice;
    item.taxRate = 13;
  });
  await gridApi.grid.insertAt(newGoods, -1);
};
</script>

<template>
  <div>
    <Grid>
      <template #toolbar-actions>
        <!--<a-checkbox class="mr-2" @change="(e: any) => (useTaxPriceCalc = e.target.checked)">-->
        <!--  按不含税单价计算-->
        <!--</a-checkbox>-->
      </template>
      <template #toolbarTools>
        <a-space>
          <!--<ImportData-->
          <!--  v-if="props.pageType !== 'audit' && props.formProps.businessStructure === BusinessStructureEnum.SALE"-->
          <!--  title="导入商品"-->
          <!--  :upload-api="importProductApi"-->
          <!--  :download-template-api="downloadProductTemplateApi"-->
          <!--  @upload-success="importSuccess"-->
          <!--/>-->
          <a-button
            v-if="props.pageType !== 'audit' && props.formProps.businessStructure === BusinessStructureEnum.SALE"
            type="primary"
            @click="modalApi.setData([]).open()"
          >
            选择商品
          </a-button>
          <!--<a-button-->
          <!--  v-if="props.pageType !== 'audit' && props.formProps.businessStructure === BusinessStructureEnum.PURCHASE"-->
          <!--  type="primary"-->
          <!--  @click="exportProductApi(productList)"-->
          <!--&gt;-->
          <!--  导出商品-->
          <!--</a-button>-->
          <a-button v-if="props.pageType !== 'audit'" @click="batchSetting"> 批量设置 </a-button>
          <a-button
            v-if="props.pageType !== 'audit' && props.formProps.businessStructure === BusinessStructureEnum.PURCHASE"
            type="primary"
            @click="resetTableData"
          >
            选择商品
          </a-button>
          <a-button v-if="props.pageType !== 'audit'" danger @click="removeAccount">删行</a-button>
        </a-space>
      </template>
      <template #edit_productName="{ row }">
        <a-input v-model:value="row.productName" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_productAlias="{ row }">
        <a-input v-model:value="row.productAlias" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_skuName="{ row }">
        <a-input v-model:value="row.specifications" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_spuCode="{ row }">
        <a-input v-model:value="row.productCode" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_measureUnit="{ row }">
        <a-input v-model:value="row.measureUnit" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_brandName="{ row }">
        <a-input v-model:value="row.brandName" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_originName="{ row }">
        <a-input v-model:value="row.originName" :disabled="props.pageType === 'audit'" />
      </template>
      <template #edit_quantity="{ row }">
        <a-input-number
          v-model:value="row.quantity"
          placeholder="请输入"
          @change="calculateRow(row)"
          :precision="4"
          :controls="false"
          :disabled="props.pageType === 'audit'"
          class="w-full"
        />
      </template>
      <template #edit_sourcePrice="{ row }">
        <a-input-number
          v-model:value="row.sourcePrice"
          placeholder="请输入"
          :precision="4"
          :controls="false"
          :disabled="props.pageType === 'audit'"
          class="w-full"
        />
      </template>
      <template #edit_priceWithTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithTax"
          @change="calculateRow(row)"
          :precision="4"
          :disabled="useTaxPriceCalc || props.pageType === 'audit'"
          :controls="false"
          class="w-full"
        />
      </template>
      <template #edit_taxRate="{ row }">
        <a-input-number
          v-model:value="row.taxRate"
          placeholder="请输入"
          @change="calculateRow(row)"
          :precision="2"
          :disabled="props.pageType === 'audit'"
          :controls="false"
          class="w-full"
        />
      </template>
      <template #edit_amountWithTax="{ row }">
        <a-input-number
          v-model:value="row.amountWithTax"
          :disabled="props.pageType === 'audit'"
          class="w-full"
          :controls="false"
        />
      </template>
      <template #edit_taxAmount="{ row }">
        <a-input-number
          v-model:value="row.taxAmount"
          :disabled="props.pageType === 'audit'"
          class="w-full"
          :controls="false"
        />
      </template>
      <template #edit_amountWithoutTax="{ row }">
        <a-input-number
          v-model:value="row.amountWithoutTax"
          :disabled="props.pageType === 'audit'"
          class="w-full"
          :controls="false"
        />
      </template>
      <template #edit_priceWithoutTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithoutTax"
          @change="calculateRow(row)"
          :precision="4"
          :disabled="!useTaxPriceCalc || props.pageType === 'audit'"
          :controls="false"
          class="w-full"
        />
      </template>
      <template #edit_remarks="{ row }">
        <a-input v-model:value="row.remarks" placeholder="请输入" :disabled="props.pageType === 'audit'" />
      </template>
      <template #sourceItemNumber="{ row }"> {{ row.sourceOrderCode }}-{{ row.sourceItemNumber }} </template>
    </Grid>
    <Modal />
    <ProductPick ref="ProductPickRef" />
    <BatchSettingModal ref="batchSettingModal" @confirm="batchSettingConfirm" />
  </div>
</template>

<style scoped></style>
