<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrderInfo } from '#/api';

import { h } from 'vue';

import { StatusTag, useModalUrl } from '@vben/base-ui';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { PlusOutlined } from '@ant-design/icons-vue';
import { message, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { changePurchaseApi, delPurchaseApi, getPurchaseListApi } from '#/api';

import Detail from './detail.vue';
import Form from './form.vue';
import SplitOrders from './split-orders.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'purchaseOrderCode',
      label: '采购订单编号',
    },
    {
      component: 'Input',
      fieldName: 'purchaseOrderName',
      label: '采购订单名称',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务结构',
      componentProps: {
        options: dictStore.getDictList('BUS_STRUCTURE'),
      },
    },
    {
      component: 'Input',
      fieldName: 'salesOrderCode',
      label: '关联销售订单编号',
    },
    {
      component: 'Input',
      fieldName: 'salesOrderName',
      label: '关联销售订单名称',
    },
    {
      component: 'Input',
      fieldName: 'supplierCompanyName',
      label: '上游企业',
    },
    {
      component: 'Input',
      fieldName: 'executorCompanyName',
      label: '贸易执行企业',
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'Select',
      fieldName: 'isCompleted',
      label: '完成状态',
      componentProps: {
        options: dictStore.getDictList('IS_COMPLETED'),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'businessDate',
      label: '业务日期',
    },
  ],
  fieldMappingTime: [
    ['businessDate', ['businessStartDate', 'businessEndDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
  wrapperClass: 'grid-cols-1 md:grid-cols-3',
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'purchaseOrderCode', title: '采购订单编号', slots: { default: 'purchaseOrderCode' }, minWidth: 200 },
    {
      field: 'purchaseOrderName',
      title: '采购订单名称',
      minWidth: 200,
    },
    {
      field: 'supplierCompanyName',
      title: '上游企业',
      minWidth: 200,
    },
    { field: 'projectCode', title: '项目编号', minWidth: 200 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'businessStructure',
      title: '业务结构',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STRUCTURE',
        },
      },
      minWidth: 100,
    },
    {
      field: 'projectModel',
      title: '项目模式',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'PROJECT_MODE',
        },
      },
      minWidth: 100,
    },
    // { field: 'salesOrderName', title: '关联订单名称', minWidth: 200 },
    { field: 'salesOrderCode', title: '关联订单编号', minWidth: 200 },
    { field: 'businessDate', title: '业务日期', formatter: 'formatDate', minWidth: 100 },
    {
      field: 'approvalStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
      minWidth: 100,
    },
    {
      field: 'status',
      title: '业务状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'BUS_STATUS',
        },
      },
      minWidth: 100,
    },
    {
      field: 'isCompleted',
      title: '完成状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'IS_COMPLETED',
          option: {
            dictValueType: 'number',
          },
        },
      },
      minWidth: 100,
    },
    // { field: 'createTime', title: '创建时间' },
    // { field: 'createBy', title: '创建人' },
    // { field: 'createDept', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPurchaseListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const [registerSplitOrders, { openPopup: openSplitOrdersPopup }] = usePopup();
const add = () => {
  openFormPopup(true, { pageType: 'edit' });
};
const edit = (row: OrderInfo) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const detail = (row: OrderInfo) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const invalidate = (row: OrderInfo, type: string) => {
  AntdModal.confirm({
    title: `确认${type}`,
    content: `此操作将${type}该数据，是否继续？`,
    async onOk() {
      const params = {
        id: row.id,
        status: row.status,
        approvalStatus: row.approvalStatus,
        isCompleted: row.isCompleted,
      };
      switch (type) {
        case '作废': {
          params.status = 'CANCELLED';

          break;
        }
        case '取消完成': {
          params.isCompleted = 0;

          break;
        }
        case '完成': {
          params.isCompleted = 1;

          break;
        }
      }
      await changePurchaseApi(params);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};

const access = (row: { id: number }) => {
  openFormPopup(true, { ...row, pageType: 'edit' });
};
const viewDetail = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'detail' });
};
const audit = (row: { id: number }) => {
  openDetailPopup(true, { ...row, pageType: 'audit' });
};

useModalUrl({
  handlers: {
    // 编辑弹层
    edit: (params) => {
      const data = {
        id: params.id,
      };
      access(data);
    },

    // 详情弹层
    detail: (params) => {
      const data = {
        id: params.id,
      };
      viewDetail(data);
    },

    // 审核弹层
    audit: (params) => {
      const data = {
        id: params.id,
        pageType: 'audit',
      };
      audit(data);
    },
  },
});

const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const del = async (row: OrderInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delPurchaseApi(row.id as number);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
const splitOrders = (row: OrderInfo) => {
  openSplitOrdersPopup(true, row);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button :icon="h(PlusOutlined)" type="primary" @click="add">
          {{ $t('base.Add') }}
        </a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <!-- 待提交才能编辑， 已拒绝的也可重新发起 -->
          <TypographyLink
            @click="edit(row)"
            v-if="row.status === 'DRAFTING' || row.approvalStatus === 'REJECTED' || row.approvalStatus === 'CANCELED'"
          >
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <!-- 待提交才能删除 -->
          <TypographyLink type="danger" @click="del(row)" v-if="row.status === 'DRAFTING'">
            {{ $t('base.del') }}
          </TypographyLink>
          <!-- 只有审批拒绝才能作废 -->
          <TypographyLink
            type="danger"
            @click="invalidate(row, '作废')"
            v-if="row.approvalStatus === 'REJECTED' || row.approvalStatus === 'CANCELED'"
          >
            {{ $t('base.invalidate') }}
          </TypographyLink>
          <!-- 未完成并且已生效才能完成 -->
          <TypographyLink @click="invalidate(row, '完成')" v-if="!row.isCompleted && row.status === 'EFFECTIVE'">
            {{ $t('base.completed') }}
          </TypographyLink>
          <TypographyLink @click="invalidate(row, '取消完成')" v-if="row.isCompleted">
            {{ $t('base.cancel') }}
          </TypographyLink>
          <TypographyLink @click="splitOrders(row)" v-if="row.status === 'EFFECTIVE'"> 拆单 </TypographyLink>
        </a-space>
      </template>
      <template #purchaseOrderCode="{ row }">
        <a-space>
          <StatusTag v-if="row.taskType !== '0'" code="TASK_TYPE" :value="row.taskType" />
          <span>{{ row.purchaseOrderCode }}</span>
        </a-space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
    <Detail @register="registerDetail" @ok="editSuccess" />
    <SplitOrders @register="registerSplitOrders" />
  </Page>
</template>

<style></style>
