<script setup lang="ts">
import type { Key } from 'ant-design-vue/es/_util/type';
import type { SelectValue } from 'ant-design-vue/es/select';

import type { OrderInfo, PurchaseOrderItemRequest, PurchaseOrderRequest, SplitPurchaseOrderInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { confirm } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { formatMoney } from '@vben/utils';

import {
  Button,
  Col,
  Descriptions,
  DescriptionsItem,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Space,
  Table,
} from 'ant-design-vue';

import { checkSalesOrderCodeApi, infoPurchaseListApi, projectManageDetailApi, splitPurchaseOrderApi } from '#/api';

const emit = defineEmits(['register', 'ok']);
const orderDetail = ref<OrderInfo>({});
const purchaserCompanyOptions = ref([]);
const state = reactive({
  globalIndex: 1,
});
const init = async (data: { id: number }) => {
  if (data.id) {
    orderDetail.value = await infoPurchaseListApi({ id: data.id });
    orderGoodsSource.value = orderDetail.value.purchaseOrderItem ?? [];
    if (orderDetail.value.projectId) {
      const projectDetail = await projectManageDetailApi(orderDetail.value.projectId);
      purchaserCompanyOptions.value = projectDetail.projectPartners.filter(
        (item: any) => item.partnerType === 'PURCHASER',
      );
    }
  }
};
const [registerPopup, { closePopup, changeOkLoading }] = usePopupInner(init);
const orderGoodsColumns = [
  {
    title: '行号',
    dataIndex: 'itemNumber',
    key: 'itemNumber',
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    key: 'productName',
  },
  {
    title: '商品编码',
    dataIndex: 'productCode',
    key: 'productCode',
  },
  {
    title: '商品别名',
    dataIndex: 'productAlias',
    key: 'productAlias',
  },
  {
    title: '规格型号',
    dataIndex: 'specifications',
    key: 'specifications',
  },
  {
    title: '单位',
    dataIndex: 'measureUnit',
    key: 'measureUnit',
  },
  {
    title: '销售含税单价',
    dataIndex: 'sourcePrice',
    key: 'sourcePrice',
  },
  {
    title: '含税单价',
    dataIndex: 'priceWithTax',
    key: 'priceWithTax',
    customRender: ({ value }: { value: number }) => formatMoney(value),
  },
  {
    title: '采购数量',
    dataIndex: 'quantity',
    key: 'quantity',
  },
];
const splitOrderGoodsColumns = [
  {
    title: '行号',
    dataIndex: 'index',
    key: 'index',
    customRender: ({ index }: { index: number }) => index + 1,
  },
  {
    title: '采购订单行号',
    dataIndex: 'itemNumber',
    key: 'itemNumber',
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    key: 'productName',
  },
  {
    title: '商品编码',
    dataIndex: 'productCode',
    key: 'productCode',
  },
  {
    title: '商品别名',
    dataIndex: 'productAlias',
    key: 'productAlias',
  },
  {
    title: '规格型号',
    dataIndex: 'specifications',
    key: 'specifications',
  },
  {
    title: '单位',
    dataIndex: 'measureUnit',
    key: 'measureUnit',
  },
  {
    title: '销售数量',
    dataIndex: 'quantity',
    key: 'quantity',
  },
  {
    title: '含税单价',
    dataIndex: 'priceWithTax',
    key: 'priceWithTax',
    customRender: ({ value }: { value: number }) => formatMoney(value),
  },
];
const orderGoodsSource = ref<PurchaseOrderItemRequest[]>([]);
const filterOrderGoods = computed(() => {
  const splitProductIds = new Set(
    splitOrders.value.flatMap((order: PurchaseOrderRequest) => order.itemList?.map((product) => product.id)),
  );
  return orderGoodsSource.value.filter((item) => !item.sourceOrderId && !splitProductIds.has(item.id ?? 0));
});
const orderGoodsSelectedRowKeys = ref<Key[]>([]);
const splitOrderGoodsSelectedRowKeys = ref<Record<number, Key[]>>({});
const onOrderGoodsSelectChange = (selectedRowKeys: Key[]) => {
  orderGoodsSelectedRowKeys.value = selectedRowKeys;
};
const onSplitOrderGoodsSelectChange = (selectedRowKeys: Key[], index: number) => {
  splitOrderGoodsSelectedRowKeys.value[index] = selectedRowKeys;
};
const splitOrders = ref<PurchaseOrderRequest[]>([]);
const handleSplit = () => {
  if (orderGoodsSelectedRowKeys.value.length === 0) {
    return message.error('请选择商品');
  }
  const itemList = orderGoodsSource.value.filter((item) => orderGoodsSelectedRowKeys.value.includes(item.id ?? 0));
  itemList.forEach((item) => {
    item.priceWithTax = item.sourcePrice || item.priceWithTax;
    item.sourceItemId = item.id;
  });
  splitOrders.value.push({
    salesOrderCode: '',
    salesOrderName: `销售订单${state.globalIndex++}`,
    purchaserCompanyCode: '',
    purchaserCompanyName: '',
    itemList,
  });
  orderGoodsSelectedRowKeys.value = [];
};
const handleSplitCompanySelect = (_value: SelectValue, option: any, item: PurchaseOrderRequest) => {
  item.purchaserCompanyName = option.companyName;
};
const delSplitOrder = async (index: number) => {
  await confirm('确认删除该订单吗？', '确认删除');
  splitOrders.value.splice(index, 1);
};
const delSplitOrderGoods = async (item: PurchaseOrderRequest, index: number) => {
  const selectedKeys = splitOrderGoodsSelectedRowKeys.value[index];
  if (!selectedKeys) {
    return message.error('请选择商品');
  }
  await confirm('确认删除选中商品吗？', '确认删除');
  item.itemList = item.itemList?.filter((product) => !selectedKeys.includes(product.id ?? 0));
};
const FormRef = ref();
const handleOk = async () => {
  if (splitOrders.value.length === 0) {
    return message.error('请先拆分订单');
  }
  await FormRef.value.validate();
  const salesOrderList: PurchaseOrderRequest[] = [];
  splitOrders.value.forEach((item) => {
    salesOrderList.push({
      ...item,
      itemList: item.itemList?.map((product) => ({
        priceWithTax: product.priceWithTax,
        sourceItemId: product.sourceItemId,
      })),
    });
  });
  const formData: SplitPurchaseOrderInfo = {
    purchaseOrderId: orderDetail.value.id,
    salesOrderList,
  };
  changeOkLoading(true);
  try {
    await splitPurchaseOrderApi(formData);
    closePopup();
    emit('ok');
  } finally {
    changeOkLoading(false);
  }
};
const validateSalesOrderCode = async (_rule: any, value: string, callback: any) => {
  if (!value || value.trim() === '') {
    callback();
  }
  const result = await checkSalesOrderCodeApi({ salesOrderCode: value.trim() });
  if (result.code === 200) {
    callback();
  } else {
    throw new Error(result.msg);
  }
};
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    title="采购订单拆单"
    show-ok-btn
    ok-text="提交"
    destroy-on-close
    @register="registerPopup"
    @ok="handleOk"
  >
    <div :class="BASE_PAGE_CLASS_NAME">
      <Descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="订单编号">
          {{ orderDetail.purchaseOrderCode }}
        </DescriptionsItem>
        <DescriptionsItem label="订单名称">
          {{ orderDetail.purchaseOrderName }}
        </DescriptionsItem>
      </Descriptions>
      <BasicCaption content="商品信息">
        <template #action>
          <Button type="primary" :disabled="orderGoodsSelectedRowKeys.length === 0" @click="handleSplit">
            拆分新订单
          </Button>
        </template>
      </BasicCaption>
      <Table
        :columns="orderGoodsColumns"
        :data-source="filterOrderGoods"
        :row-selection="{ selectedRowKeys: orderGoodsSelectedRowKeys, onChange: onOrderGoodsSelectChange }"
        row-key="id"
        :pagination="false"
      />
      <BasicCaption content="销售订单预览" class="mt-4" />
      <Form ref="FormRef" :model="splitOrders" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
        <template v-for="(item, index) in splitOrders" :key="index">
          <Row class="mt-5">
            <Col :span="8">
              <FormItem
                label="销售订单编号"
                :name="[index, 'salesOrderCode']"
                :rules="[{ validator: validateSalesOrderCode, trigger: 'blur' }]"
              >
                <Input v-model:value="item.salesOrderCode" placeholder="留空则自动生成" />
              </FormItem>
            </Col>
            <Col :span="8">
              <FormItem label="销售订单名称" :name="[index, 'salesOrderName']" :required="true">
                <Input v-model:value="item.salesOrderName" />
              </FormItem>
            </Col>
            <Col :span="8">
              <FormItem label="下游企业" :name="[index, 'purchaserCompanyCode']" :required="true">
                <Select
                  v-model:value="item.purchaserCompanyCode"
                  :options="purchaserCompanyOptions"
                  show-search
                  :filter-option="(input: string, option: any) => option.label.includes(input)"
                  :field-names="{ label: 'companyName', value: 'companyCode' }"
                  @change="(value: SelectValue, option: any) => handleSplitCompanySelect(value, option, item)"
                />
              </FormItem>
            </Col>
          </Row>
          <div class="mb-4 text-right">
            <Space>
              <Button type="primary" danger @click="delSplitOrder(index)"> 删除订单 </Button>
              <Button type="primary" @click="delSplitOrderGoods(item, index)"> 删除选中商品 </Button>
            </Space>
          </div>
          <Table
            :columns="splitOrderGoodsColumns"
            :data-source="item.itemList"
            :row-selection="{
              selectedRowKeys: splitOrderGoodsSelectedRowKeys[index],
              onChange: (selectedRowKeys) => onSplitOrderGoodsSelectChange(selectedRowKeys, index),
            }"
            row-key="id"
            :pagination="false"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'priceWithTax'">
                <InputNumber v-model:value="record.priceWithTax" :precision="2" :controls="false" class="w-full" />
              </template>
            </template>
          </Table>
        </template>
      </Form>
    </div>
  </BasicPopup>
</template>

<style></style>
