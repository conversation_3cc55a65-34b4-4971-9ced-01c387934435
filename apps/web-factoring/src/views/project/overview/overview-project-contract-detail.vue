<script setup lang="ts">
import type { CollateralVO, ContractInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { cloneDeep, formatDate, formatMoney } from '@vben/utils';

import { getProjectContractDetail } from '#/api';
import { ContractList } from '#/components';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const { WorkflowPreviewModal, useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading } =
  useWorkflowBase();
const pageType = ref('detail');
const ViewButton = useViewButton();
const OperationButton = useOperationButton();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

const contractForm = ref<ContractInfo>({});
// 添加初始化方法
const init = async (data: CollateralVO) => {
  if (data) {
    pageType.value = data.pageType ?? 'detail';
    await initWorkflow({ formKey: 'fct_project_contract', businessKey: data.id });
    const detailRes = await getProjectContractDetail(data.id as number);
    contractForm.value = cloneDeep(detailRes) || {};
  }
};

const workflowSuccess = () => {
  emit('ok');
  closePopup();
};

const [registerPopup, { closePopup }] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="关联项目">
          {{ contractForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="关联用信申请" v-if="contractForm.projectType === 'comprehensive'">
          {{ contractForm.projectApplyName }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="总经办决策" v-if="contractForm.projectType === 'single'" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp" v-if="contractForm.projectType === 'single'">
        <a-descriptions-item label="决策日期">
          {{ formatDate(contractForm.decisionDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="授信期限（个月）">
          {{ contractForm.creditTerm }}
        </a-descriptions-item>
        <a-descriptions-item label="授信到期日">
          {{ formatDate(contractForm.creditExpireDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="授信额度（元）">
          {{ formatMoney(contractForm.creditAmount) }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="主合同信息" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="保理合同名称">
          {{ contractForm.contractName }}
        </a-descriptions-item>
        <a-descriptions-item label="保理合同编号">
          {{ contractForm.contractCode }}
        </a-descriptions-item>
        <a-descriptions-item label="签署日期">
          {{ formatDate(contractForm.signDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="合同日期范围">
          {{ formatDate(contractForm.contractStartDate) }} ~ {{ formatDate(contractForm.contractEndDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="合同期限（个月）">
          {{ contractForm.contractTerm }}
        </a-descriptions-item>
        <a-descriptions-item label="合同金额（元）">
          {{ formatMoney(contractForm.contractAmount) }}
        </a-descriptions-item>
      </a-descriptions>

      <ContractList
        v-model="contractForm.contractList"
        :business-id="contractForm.id"
        :business-type="
          contractForm.projectType === 'comprehensive' ? 'FCT_PROJECT_APPLY_CONTRACT' : 'FCT_PROJECT_CONTRACT'
        "
        :edit-mode="false"
      >
        <template #header>
          <BasicCaption content="未盖章需审批合同" class="mb-4" />
        </template>
      </ContractList>
      <ContractList
        v-model="contractForm.sealContractList"
        :business-id="contractForm.id"
        :business-type="
          contractForm.projectType === 'comprehensive' ? 'FCT_PROJECT_APPLY_CONTRACT_SEAL' : 'FCT_PROJECT_CONTRACT_SEAL'
        "
        :edit-mode="false"
      >
        <template #header>
          <BasicCaption content="盖章合同" class="mb-4" />
        </template>
      </ContractList>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped></style>
