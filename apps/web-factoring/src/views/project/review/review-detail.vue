<script setup lang="ts">
import type { ReviewInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getReviewInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const { WorkflowPreviewModal, useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading } =
  useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: ReviewInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: data.formKey, businessKey: data.id });
  reviewForm.value = data.id ? await getReviewInfoApi(data.id as number) : data;
};
const reviewForm = ref<ReviewInfo>({});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="项目评审会议信息" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联项目">
          {{ reviewForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目评审会议名称">
          {{ reviewForm.reviewNodeName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目背景" :span="2">
          {{ reviewForm.backgroundDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="项目风险点及建议" :span="2">
          {{ reviewForm.riskMitigationDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="项目结论" :span="2">
          {{ reviewForm.resultDesc }}
        </a-descriptions-item>
      </a-descriptions>
      <BaseAttachmentList :business-id="reviewForm.id" business-type="FCT_PROJECT_MEETING_REVIEW" />
      <BaseAttachmentList :business-id="reviewForm.id" business-type="FCT_PROJECT_MEETING_REVIEW_NOTIFY">
        <template #header>
          <BasicCaption content="运营事务告知函" />
        </template>
      </BaseAttachmentList>
      <BaseAttachmentList :business-id="reviewForm.id" business-type="FCT_PROJECT_MEETING_REVIEW_MINUTES">
        <template #header>
          <BasicCaption content="评审会议纪要" />
        </template>
      </BaseAttachmentList>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
