<script setup lang="ts">
import type { CreditPricingInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getCreditPricingInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import RepaymentCalculationHistory from '#/views/project/components/repayment-calculation-history.vue';
import BaseDetail from '#/views/project/credit-pricing/components/base-detail.vue';
import PricingScheme from '#/views/project/credit-pricing/components/pricing-scheme.vue';

const emit = defineEmits(['ok', 'register']);
const { WorkflowPreviewModal, useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading } =
  useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: CreditPricingInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_project_credit_pricing', businessKey: data.id });
  let info = data.id ? await getCreditPricingInfoApi(data.id as number) : data;
  const calculation = omit(
    info.calculation,
    'id',
    'targetCompanyName',
    'targetCompanyCode',
    'projectCode',
    'projectName',
  );
  info = {
    ...info,
    ...calculation,
  };
  creditPricingForm.value = info;
  RepaymentCalculationHistoryRef.value.init(creditPricingForm.value);
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const RepaymentCalculationHistoryRef = ref();
const creditPricingForm = ref<CreditPricingInfo>({});
</script>

<template>
  <BasicPopup title="用信定价" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :credit-pricing-form="creditPricingForm" :descriptions-prop="descriptionsProp" />
      <PricingScheme :credit-pricing-form="creditPricingForm" :descriptions-prop="descriptionsProp" />
      <DebtServiceDetail
        :debt-service-form="creditPricingForm"
        :descriptions-prop="descriptionsProp"
        debt-type="creditPricing"
      />
      <RepaymentCalculationDetail
        :calculation-form="creditPricingForm"
        :descriptions-prop="descriptionsProp"
        calculation-type="CreditPricing"
      />
      <RepaymentCalculationHistory ref="RepaymentCalculationHistoryRef" calculation-type="CreditPricing" />
      <BaseAttachmentList :business-id="creditPricingForm.id" business-type="FCT_PROJECT_CREDIT_PRICING" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
