<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { InitiationInfo, ProjectCompanyBO, ProjectUserBO } from '#/api';

import { nextTick, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { Select } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import { isEmpty } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getCompanyListApi, getUserListApi } from '#/api';
import AccountsReceivablePool from '#/views/project/components/accounts-receivable-pool.vue';
import AccountsReceivable from '#/views/project/components/accounts-receivable.vue';
import Mortgage from '#/views/project/components/mortgage.vue';
import Pledge from '#/views/project/components/pledge.vue';

const dictStore = useDictStore();
const initiationForm = defineModel<InitiationInfo>({ type: Object, required: true });
const colSpan = COL_SPAN_PROP;
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
// 验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入项目名称' }],
  targetIndustry: [{ required: true, message: '请选择投向行业', trigger: 'change' }],
  isSupportRealEconomy: [{ required: true, message: '请选择是否支持实体经济', trigger: 'change' }],
  isProvincialKeyIndustry: [{ required: true, message: '请选择是否为省重点产业', trigger: 'change' }],
  factoringType: [{ required: true, message: '请选择保理类型', trigger: 'change' }],
  creditor: [{ required: true, message: '请选择债权人', trigger: 'change' }],
  // debtor: [{ required: true, message: '请选择债务人', trigger: 'change' }],
  // guarantor: [{ required: true, message: '请选择担保人', trigger: 'change' }],
  creditType: [{ required: true, message: '请选择是否循环额度', trigger: 'change' }],
  financingAmount: [{ required: true, message: '请输入保理融资金额' }],
  financingRatio: [{ required: true, message: '请输入保理融资比例' }],
  estimateIncomeRate: [{ required: true, message: '请输入预估综合收益率' }],
};

// 保存数据时转换关联企业数据
const saveCompanyList = (): ProjectCompanyBO[] => {
  const companyList: ProjectCompanyBO[] = [];

  // 添加担保企业
  if (initiationForm.value.guarantor?.length > 0) {
    initiationForm.value.guarantor.forEach((companyCode: string) => {
      if (companyCode) {
        companyList.push({
          companyCode,
          companyName: getCompanyLabel(companyCode),
          projectCompanyType: 'guarantee',
        });
      }
    });
  }

  // 添加债权人
  if (initiationForm.value.creditor?.length > 0) {
    initiationForm.value.creditor.forEach((companyCode: string) => {
      if (companyCode) {
        companyList.push({
          companyCode,
          companyName: getCompanyLabel(companyCode),
          projectCompanyType: 'creditor',
        });
      }
    });
  }

  // 添加债务人
  if (initiationForm.value.debtor?.length > 0) {
    initiationForm.value.debtor.forEach((companyCode: string) => {
      if (companyCode) {
        companyList.push({
          companyCode,
          companyName: getCompanyLabel(companyCode),
          projectCompanyType: 'debtor',
        });
      }
    });
  }

  return companyList;
};
// 公司列表
const companyOptions = ref<{ companyCode: string; companyName: string }[]>([]);

const loadCompanyOptions = async () => {
  companyOptions.value = await getCompanyListApi();
};

// 初始化时加载公司列表
loadCompanyOptions();

// 获取公司名称
const getCompanyLabel = (companyCode: string): string => {
  if (!companyCode) return '';

  // 从缓存中查找
  const company = companyOptions.value.find((item) => item.companyCode === companyCode);

  return company?.companyName || '';
};

const userOptions = ref<{ userId: number; userName: string }[]>([]);

const loadUserOptions = async () => {
  userOptions.value = await getUserListApi();
};

loadUserOptions();

const filterOption = (input: string, option: any) => {
  return option.realName.toLowerCase().includes(input.toLowerCase());
};

// 获取人员名称
const getUserLabel = (userId: string): string => {
  if (!userId) return '';

  // 从缓存中查找
  const user = userOptions.value.find((item) => item.id === userId);

  return user?.realName || '';
};

// 保存数据时转换相关人员数据
const saveUserList = (): ProjectUserBO[] => {
  const userList: ProjectUserBO[] = [];

  // 添加业务经理
  if (initiationForm.value.businessManager) {
    userList.push({
      userId: initiationForm.value.businessManager,
      userName: getUserLabel(initiationForm.value.businessManager),
      projectUserType: 'business',
    });
  }

  // 添加运营经理
  if (initiationForm.value.operationsManager) {
    userList.push({
      userId: initiationForm.value.operationsManager,
      userName: getUserLabel(initiationForm.value.operationsManager),
      projectUserType: 'operations',
    });
  }

  // 添加风控经理
  if (initiationForm.value.riskControlManager) {
    userList.push({
      userId: initiationForm.value.riskControlManager,
      userName: getUserLabel(initiationForm.value.riskControlManager),
      projectUserType: 'risk',
    });
  }

  // 添加财务经理
  if (initiationForm.value.financialManager) {
    userList.push({
      userId: initiationForm.value.financialManager,
      userName: getUserLabel(initiationForm.value.financialManager),
      projectUserType: 'finance',
    });
  }

  return userList;
};
const formRef = ref();
const AccountsReceivableRef = ref();
const PledgeRef = ref();
const MortgageRef = ref();
const save = async () => {
  await formRef.value.validate();
  await AccountsReceivableRef.value.save();
  await MortgageRef.value.save();
  await PledgeRef.value.save();
  // 保存关联企业数据
  initiationForm.value.companyList = saveCompanyList();

  // 保存相关人员数据
  initiationForm.value.userList = saveUserList();

  return initiationForm.value;
};
const init = (form: InitiationInfo) => {
  nextTick(() => {
    AccountsReceivableRef.value.init(form);
  });
  MortgageRef.value.init(form);
  PledgeRef.value.init(form);
};
const oldFactoringType = ref<string>('');
const handleFactoringTypeChange = () => {
  oldFactoringType.value = initiationForm.value.factoringType || '';
  if (initiationForm.value.factoringType !== 'pool_factoring' && oldFactoringType.value === 'pool_factoring') {
    initiationForm.value.receivableRefList = [];
    AccountsReceivableRef.value.init(initiationForm.value);
  }
  if (initiationForm.value.factoringType === 'pool_factoring' && oldFactoringType.value !== 'pool_factoring') {
    initiationForm.value.receivableRefList = [];
    AccountsReceivableRef.value.init(initiationForm.value);
  }
};
const changeReceivable = () => {
  if (!isEmpty(initiationForm.value.receivableRefList) && initiationForm.value.financingAmount) {
    let receivableAmount = new BigNumber(0);
    initiationForm.value.receivableRefList.forEach((item) => {
      receivableAmount = receivableAmount.plus(
        new BigNumber(
          item[initiationForm.value.factoringType === 'pool_factoring' ? 'poolTotalAmount' : 'receivableAmount'] || 0,
        ),
      );
    });
    const financingAmount = new BigNumber(initiationForm.value.financingAmount);
    initiationForm.value.financingRatio = financingAmount
      .div(receivableAmount)
      .times(100)
      .decimalPlaces(2, BigNumber.ROUND_HALF_UP)
      .toNumber();
  } else {
    initiationForm.value.financingRatio = 0;
  }
};
defineExpose({ init, save });
</script>

<template>
  <a-form ref="formRef" :model="initiationForm" :rules="rules" v-bind="formProp" class="">
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="项目类型" name="projectType">
          <a-select
            v-model:value="initiationForm.projectType"
            :options="dictStore.getDictList('FCT_PROJECT_TYPE')"
            disabled
            placeholder="请选择项目类型"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="投向行业" name="targetIndustry">
          <a-select
            v-model:value="initiationForm.targetIndustry"
            :options="dictStore.getDictList('FCT_TARGET_INDUSTRY')"
            placeholder="请选择投向行业"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="合作企业" name="targetCompanyCode">
          <ApiComponent
            v-model="initiationForm.targetCompanyCode as unknown as string"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
            :disabled="true"
            placeholder="请选择合作企业"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="地市" name="cityName">
          <a-input v-model:value="initiationForm.cityName" disabled placeholder="请输入地市" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="区县" name="districtName">
          <a-input v-model:value="initiationForm.districtName" disabled placeholder="请输入区县" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="是否支持实体经济" name="isSupportRealEconomy">
          <a-radio-group
            v-model:value="initiationForm.isSupportRealEconomy"
            :options="dictStore.getDictList('baseBooleanType')"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="是否为省重点产业" name="isProvincialKeyIndustry">
          <a-radio-group
            v-model:value="initiationForm.isProvincialKeyIndustry"
            :options="dictStore.getDictList('baseBooleanType')"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="项目名称" name="projectName">
          <a-input v-model:value="initiationForm.projectName" placeholder="请输入项目名称" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="项目编号" name="projectCode">
          <a-input v-model:value="initiationForm.projectCode" disabled placeholder="请输入项目编号" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 业务类型 -->
    <BasicCaption content="业务类型" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="保理类型" name="factoringType">
          <a-select
            v-model:value="initiationForm.factoringType"
            :options="dictStore.getDictList('FCT_FACTORING_TYPE')"
            @change="handleFactoringTypeChange"
            placeholder="请选择保理类型"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="支持操作模式" name="supportMode">
          <a-radio-group
            v-model:value="initiationForm.supportMode"
            :options="dictStore.getDictList('FCT_SUPPORT_MODE')"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="支持保理方向" name="factoringDirection">
          <a-radio-group
            v-model:value="initiationForm.factoringDirection"
            :options="dictStore.getDictList('FCT_FACTORING_DIRECTION')"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="追索权要求" name="recourseRequired">
          <a-radio-group
            v-model:value="initiationForm.recourseRequired"
            :options="dictStore.getDictList('FCT_RECOURES_REQUIRED')"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 其他情况说明 -->
    <BasicCaption content="项目背景" />
    <a-row class="mt-5">
      <a-col :span="24">
        <a-form-item label="项目背景" name="backgroundDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.backgroundDesc" :rows="4" placeholder="请输入项目背景" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 交易主体信息 -->
    <BasicCaption content="交易主体信息" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="债权人" name="creditor">
          <ApiComponent
            v-model="initiationForm.creditor as unknown as any"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
            mode="multiple"
            show-search
            :filter-option="(input: string, option: any) => option.label.includes(input)"
            placeholder="请选择债权人"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="担保人" name="guarantor">
          <ApiComponent
            v-model="initiationForm.guarantor as unknown as string"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
            mode="multiple"
            show-search
            :filter-option="(input: string, option: any) => option.label.includes(input)"
            placeholder="请选择担保人"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="债务人" name="debtor">
          <ApiComponent
            v-model="initiationForm.debtor as unknown as any"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
            mode="multiple"
            show-search
            :filter-option="(input: string, option: any) => option.label.includes(input)"
            placeholder="请选择债务人"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="债权人基本情况" name="creditorInfoDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.creditorInfoDesc" :rows="4" placeholder="请输入债权人基本情况" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="债务人基本情况" name="debtorInfoDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.debtorInfoDesc" :rows="4" placeholder="请输入债务人基本情况" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="担保人基本情况" name="guarantorInfoDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.guarantorInfoDesc" :rows="4" placeholder="请输入担保人基本情况" />
        </a-form-item>
      </a-col>
    </a-row>
    <AccountsReceivable
      v-if="initiationForm.factoringType !== 'pool_factoring'"
      ref="AccountsReceivableRef"
      v-model="initiationForm"
      @change-receivable="changeReceivable"
    />
    <AccountsReceivablePool
      v-if="initiationForm.factoringType === 'pool_factoring'"
      ref="AccountsReceivableRef"
      v-model="initiationForm"
      @change-receivable="changeReceivable"
    />
    <a-row>
      <a-col :span="24">
        <a-form-item
          :label="`应收账款${initiationForm.factoringType === 'pool_factoring' ? '池描述' : '描述'}`"
          name="receivableDesc"
          v-bind="fullProp"
        >
          <a-textarea
            v-model:value="initiationForm.receivableDesc"
            :rows="4"
            :placeholder="`请输入应收账款${initiationForm.factoringType === 'pool_factoring' ? '池描述' : '描述'}`"
          />
        </a-form-item>
      </a-col>
    </a-row>
    <!-- 交易方案 -->
    <BasicCaption content="交易方案" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="客户测算额度（元）" name="creditCalculateAmount">
          <a-input-number
            v-model:value="initiationForm.creditCalculateAmount"
            disabled
            placeholder="请输入客户测算额度（元）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="客户可用额度（元）" name="creditCalculateAvailableAmount">
          <a-input-number
            v-model:value="initiationForm.creditCalculateAvailableAmount"
            disabled
            placeholder="请输入客户可用额度（元）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="保理融资金额（元）" name="financingAmount">
          <a-input-number
            v-model:value="initiationForm.financingAmount"
            @blur="changeReceivable"
            placeholder="请输入保理融资金额（元）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="保理融资比例（%）" name="financingRatio">
          <a-input-number
            v-model:value="initiationForm.financingRatio"
            placeholder="请输入保理融资比例（%）"
            disabled
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="预估综合收益率（%）" name="estimateIncomeRate">
          <a-input-number
            v-model:value="initiationForm.estimateIncomeRate"
            placeholder="请输入预估综合收益率（%）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="服务费（元）" name="serviceAmount">
          <a-input-number
            v-model:value="initiationForm.serviceAmount"
            placeholder="请输入服务费（元）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="授信额度类型" name="creditType">
          <a-radio-group
            v-model:value="initiationForm.creditType"
            :options="dictStore.getDictList('FCT_CREDIT_TYPE')"
          />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="增信措施描述" name="creditEnhancementDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.creditEnhancementDesc" :rows="4" placeholder="请输入增信措施描述" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="风控措施描述" name="riskControlDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.riskControlDesc" :rows="4" placeholder="请输入风控措施描述" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="资金用途" name="fundUsageDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.fundUsageDesc" :rows="4" placeholder="请输入资金用途" />
        </a-form-item>
      </a-col>
    </a-row>
    <Mortgage ref="MortgageRef" v-model="initiationForm" />
    <Pledge ref="PledgeRef" v-model="initiationForm" />
    <!-- 额度说明 -->
    <BasicCaption content="额度说明" />
    <a-row class="mt-5">
      <a-col :span="24">
        <a-form-item label="额度说明" name="creditLimitDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.creditLimitDesc" :rows="4" placeholder="请输入额度说明" />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 项目负责人 -->
    <BasicCaption content="项目负责人" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="业务经理" name="businessManager">
          <a-select
            v-model:value="initiationForm.businessManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
            placeholder="请选择业务经理"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="风控经理" name="riskControlManager">
          <a-select
            v-model:value="initiationForm.riskControlManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
            placeholder="请选择风控经理"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="运营经理" name="operationsManager">
          <a-select
            v-model:value="initiationForm.operationsManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
            placeholder="请选择运营经理"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="财务经理" name="financialManager">
          <a-select
            v-model:value="initiationForm.financialManager"
            show-search
            :options="userOptions"
            :field-names="{ label: 'realName', value: 'id' }"
            :filter-option="filterOption"
            placeholder="请选择财务经理"
          />
        </a-form-item>
      </a-col>
    </a-row>

    <!-- 其他情况说明 -->
    <BasicCaption content="其他情况说明" />
    <a-row class="mt-5">
      <a-col :span="24">
        <a-form-item label="其他情况说明" name="creditOtherDesc" v-bind="fullProp">
          <a-textarea v-model:value="initiationForm.creditOtherDesc" :rows="4" placeholder="请输入其他情况说明" />
        </a-form-item>
      </a-col>
    </a-row>
    <BaseAttachmentList
      v-model="initiationForm.attachmentList"
      :business-id="initiationForm.id"
      business-type="FCT_PROJECT_PROPOSAL"
      edit-mode
    />
  </a-form>
</template>
