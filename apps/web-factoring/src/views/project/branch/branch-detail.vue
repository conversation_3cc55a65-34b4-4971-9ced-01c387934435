<script setup lang="ts">
import type { BranchInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getBranchInfoApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const { WorkflowPreviewModal, useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading } =
  useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: BranchInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_project_meeting_party', businessKey: data.id });
  branchForm.value = data.id ? await getBranchInfoApi(data.id as number) : data;
};
const branchForm = ref<BranchInfo>({});
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="党支部会议详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联项目评审会议">
          {{ branchForm.projectReviewName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目名称">
          {{ branchForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="党支部会议名称" :span="2">
          {{ branchForm.reviewNodeName }}
        </a-descriptions-item>
        <a-descriptions-item label="项目背景" :span="2">
          {{ branchForm.backgroundDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="项目风险点及建议" :span="2">
          {{ branchForm.riskMitigationDesc }}
        </a-descriptions-item>
        <a-descriptions-item label="项目结论" :span="2">
          {{ branchForm.resultDesc }}
        </a-descriptions-item>
      </a-descriptions>
      <BaseAttachmentList :business-id="branchForm.id" business-type="FCT_PROJECT_MEETING_PARTY" />
      <BaseAttachmentList :business-id="branchForm.id" business-type="FCT_PROJECT_MEETING_PARTY_MINUTES">
        <template #header>
          <BasicCaption content="党支部会议纪要" />
        </template>
      </BaseAttachmentList>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
