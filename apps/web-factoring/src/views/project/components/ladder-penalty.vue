<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { $t } from '@vben/locales';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { getCombinedErrorMessagesString } from '@vben/utils';

import { message } from 'ant-design-vue';
import { isNaN } from 'lodash-es';

const penaltyForm = defineModel({ type: Object, required: true });
const init = (data: any) => {
  if (data.penaltySteps) {
    data.penaltySteps = JSON.parse(data.penaltySteps) || [];
    LadderGridApi.grid.reloadData(data.penaltySteps);
  }
};
const baseGridOptions = {
  showOverflow: 'title',
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const ladderGridOptions = {
  columns: [
    {
      field: 'daysInfo',
      title: '逾期天数（天）',
      slots: { default: 'edit_days_info' },
    },
    {
      field: 'rate',
      title: '年化罚息利率（%）',
      slots: { default: 'edit_rate' },
    },
    {
      title: '操作', // 列标题
      slots: { default: 'action' }, // 绑定#action插槽
      align: 'center', // 内容居中
      minWidth: 80, // 固定宽度
    },
  ],
  editRules: {
    rate: [{ required: true, content: '请输入年化罚息利率' }],
    daysInfo: [
      {
        validator({ row }) {
          if (!row.endDays && !row.lastDays) {
            return new Error('请输入逾期天数');
          }
          return true;
        },
      },
    ],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [LadderGrid, LadderGridApi] = useVbenVxeGrid({
  gridOptions: ladderGridOptions,
});
const addLadder = () => {
  const { visibleData } = LadderGridApi.grid.getTableData(); // 获取当前所有行数据
  const newRow = visibleData.length === 0 ? { startDays: 1, endDays: '' } : { startDays: '', endDays: '' };
  LadderGridApi.grid.insertAt(newRow, -1);
  getLastEndDays();
};
const getLastEndDays = async () => {
  const { visibleData } = LadderGridApi.grid.getTableData();
  visibleData.forEach((row, index) => {
    if (index > 0) {
      const prevRow = visibleData[index - 1];
      row.startDays = prevRow.endDays ? Number(prevRow.endDays) + 1 : '';
    }
    row.lastDays = index === visibleData.length - 1 && index !== 0;
  });
  await LadderGridApi.grid.reloadData(visibleData);
};
const endDaysChange = (currentRow: any) => {
  const { visibleData } = LadderGridApi.grid.getTableData();
  const currentIndex = visibleData.indexOf(currentRow);
  if (currentIndex !== -1 && currentIndex < visibleData.length - 1) {
    const nextRow = visibleData[currentIndex + 1];
    const endDaysValue = Number(currentRow.endDays);
    if (!isNaN(endDaysValue)) {
      nextRow.startDays = endDaysValue + 1;
    }
  }
};
const removeLadder = (data: any) => {
  LadderGridApi.grid.remove(data);
};
const save = async () => {
  const errMap = await LadderGridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
    }
  }
  const { visibleData } = LadderGridApi.grid.getTableData();
  penaltyForm.value.penaltySteps = JSON.stringify(visibleData);
  return penaltyForm.value;
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <LadderGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="addLadder">新增</a-button>
        </a-space>
      </template>
      <template #edit_days_info="{ row }">
        <a-space>
          <a-input v-model:value="row.startDays" disabled />
          ~
          <a-input v-if="!row.lastDays" v-model:value="row.endDays" @blur="endDaysChange(row)" />
          <span v-else>无限</span>
        </a-space>
      </template>
      <template #edit_rate="{ row }">
        <a-input v-model:value="row.rate" />
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link type="danger" @click="removeLadder(row)">
            {{ $t('base.del') }}
          </a-typography-link>
        </a-space>
      </template>
    </LadderGrid>
  </div>
</template>
