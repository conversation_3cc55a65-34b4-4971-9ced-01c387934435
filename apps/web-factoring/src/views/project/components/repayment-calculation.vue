<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { ProjectUseCalculationBO, ProjectUseCalculationDetailBO } from '#/api';

import { computed } from 'vue';

import { COL_SPAN_PROP } from '@vben/constants';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { cloneDeep, getCombinedErrorMessagesString, isEmpty } from '@vben/utils';

import { SyncOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { isNaN } from 'lodash-es';

import {
  calculationCreditApply,
  calculationCreditApplyInterest,
  calculationCreditPricing,
  calculationCreditPricingInterest,
  calculationPaymentConfirm,
  calculationPaymentConfirmInterest,
  calculationPricing,
  calculationPricingInterest,
  calculationRateProject,
} from '#/api';

const props = defineProps({
  calculationType: { type: String, default: () => '' },
});
const repaymentOptions = computed(() => {
  return dictStore.getDictList('FCT_REPAYMENT_ITEM').map((item) => ({
    ...item,
    disabled: item.value === 'loan_service' || item.value === 'clearing_principal_interest',
  }));
});
const calculationForm = defineModel<ProjectUseCalculationBO>({ type: Object, required: true });
const dictStore = useDictStore();
const colSpan = COL_SPAN_PROP;
const init = async (data: ProjectUseCalculationBO) => {
  await CalculationGridApi.grid.reloadData(processRepaymentItems(data.detailList ?? []));
};
const baseGridOptions = {
  showOverflow: 'title',
  keepSource: true,
  showFooter: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const expectedDateChange = () => {
  const { expectedLaunchDate, expectedDueDate } = calculationForm.value;

  if (expectedLaunchDate && expectedDueDate) {
    // 关键修复：确保转换为数字类型
    const startTimestamp = Number(expectedLaunchDate);
    const endTimestamp = Number(expectedDueDate);

    // 解析为日期对象
    const start = dayjs(startTimestamp);
    const end = dayjs(endTimestamp);

    // 计算天数差
    const daysDiff = end.diff(start, 'day');

    calculationForm.value.expectedInterestDays = Math.max(0, daysDiff);
  }
};
const calculationGridOptions = {
  columns: [
    {
      field: 'repayPeriods',
      title: '还款期数',
      minWidth: '100px',
    },
    {
      field: 'repaymentItem',
      title: '还款项',
      slots: { default: 'edit_repayment_item' },
      minWidth: '160px',
    },
    {
      field: 'currentOperationDate',
      title: '当期还本/付息日',
      slots: { default: 'edit_current_date' },
      minWidth: '180px',
    },
    {
      field: 'totalAmount',
      title: '当期净现金流(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      slots: { default: 'edit_principal_amount' },
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      slots: { default: 'edit_interest_amount' },
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'graceInterestAmount',
      title: '应还宽限期利息(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
    {
      field: 'overdueInterestAmount',
      title: '应还逾期罚息(元)',
      minWidth: '180px',
      formatter: 'formatMoney',
    },
  ],
  editRules: {
    currentOperationDate: [{ required: true, content: '请选择当期还本/付息日', trigger: 'change' }],
    repaymentItem: [{ required: true, content: '请选择还款项', trigger: 'change' }],
    totalAmount: [{ required: true, content: '请输入当期净现金流' }],
    principalAmount: [{ required: true, content: '请输入应还本金' }],
    interestAmount: [{ required: true, content: '请输入应还利息' }],
  },
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let interestAmount = new BigNumber(0);
    let serviceAmount = new BigNumber(0);
    let totalAmount = new BigNumber(0);
    let principalAmount = new BigNumber(0);
    data.forEach((item) => {
      interestAmount = interestAmount.plus(new BigNumber(item.interestAmount || 0));
      serviceAmount = serviceAmount.plus(new BigNumber(item.serviceAmount || 0));
      totalAmount = totalAmount.plus(new BigNumber(item.totalAmount || 0));
      principalAmount = principalAmount.plus(new BigNumber(item.principalAmount || 0));
    });
    const footerRow = {
      checkbox: '合计',
      interestAmount: interestAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      serviceAmount: serviceAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      totalAmount: totalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
      principalAmount: principalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber(),
    };
    return [footerRow];
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});
/**
 * 校验还款计划的日期顺序：上一期的当期还本/付息日必须早于下一期
 * @param detailList 还款计划明细列表
 * @returns 校验通过返回true，否则返回错误提示
 */
const validateRepaymentDates = (detailList: ProjectUseCalculationDetailBO[]): boolean | string => {
  if (detailList.length < 2) return true; // 少于2期，无需校验

  for (let i = 1; i < detailList.length; i++) {
    const prevItem = detailList[i - 1];
    const currentItem = detailList[i];

    // 转换为时间戳（处理前端存储的字符串格式）
    const prevTimestamp = Number(prevItem.currentOperationDate);
    const currentTimestamp = Number(currentItem.currentOperationDate);

    // 校验日期格式（避免无效值）
    if (isNaN(prevTimestamp) || isNaN(currentTimestamp)) {
      return '当期还本/付息日格式错误，请检查日期！';
    }

    // 校验时间顺序
    if (prevTimestamp >= currentTimestamp) {
      return `第${i}期的当期还本/付息日必须晚于第${i - 1}期！`;
    }
  }
  return true;
};
const calculation = async (type: string) => {
  if (type === 'interest') {
    const errMap = await CalculationGridApi.grid.validate(true);
    if (errMap) {
      const errMessage = getCombinedErrorMessagesString(errMap);
      if (errMessage) {
        message.error(errMessage);
        return null;
      }
    }
  }
  const formData = cloneDeep(calculationForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const detailList = cloneDeep(visibleData || []);
  // 新增：校验日期顺序
  if (type === 'interest') {
    const dateValidateResult = validateRepaymentDates(detailList);
    if (dateValidateResult !== true) {
      message.error(dateValidateResult as string);
      return; // 校验失败，终止试算
    }
  }
  if (!isEmpty(detailList)) {
    detailList.forEach((item: any) => {
      item.currentOperationDate = Number(item.currentOperationDate);
    });
  }
  formData.detailList = detailList;
  const api =
    type === 'interest'
      ? {
          Pricing: calculationPricingInterest,
          CreditPricing: calculationCreditPricingInterest,
          CreditApply: calculationCreditApplyInterest,
          PaymentRecord: calculationPaymentConfirmInterest,
        }[props.calculationType]
      : {
          Pricing: calculationPricing,
          CreditPricing: calculationCreditPricing,
          CreditApply: calculationCreditApply,
          PaymentRecord: calculationPaymentConfirm,
        }[props.calculationType];
  if (api) {
    const res = await api(formData);
    calculationForm.value.expectedRepayPeriods = res.length - 1;
    await CalculationGridApi.grid.reloadData(processRepaymentItems(res));
    message.success('试算成功');
  }
};
const calculationRate = async () => {
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const detailList = cloneDeep(visibleData || []);
  // 新增：校验日期顺序
  const dateValidateResult = validateRepaymentDates(detailList);
  if (dateValidateResult !== true) {
    message.error(dateValidateResult as string);
    return; // 校验失败，终止试算
  }
  if (!isEmpty(detailList)) {
    detailList.forEach((item: object) => {
      item.currentOperationDate = Number(item.currentOperationDate);
    });
  }
  const res = await calculationRateProject(detailList);
  calculationForm.value.xirrRate = res || '';
  calculationForm.value.pricingXirrRate = res || '';
  message.success('试算成功');
};
const processRepaymentItems = (items: object[]) => {
  if (!isEmpty(items)) {
    return items.map((item: object, index: number) => {
      return {
        ...item,
        currentOperationDate: item.currentOperationDate ? dayjs(item.currentOperationDate).valueOf().toString() : '',
        repaymentItemDisabled: index === 0 || index === items.length - 1,
        currentDateDisabled: index === 0 || index === items.length - 1,
        principalAmountDisabled: index === 0,
        interestAmountDisabled: index === 0,
      };
    });
  }
  return items;
};
const repaymentItemChange = (row: any) => {
  if (row.repaymentItem === 'repay_interest') row.principalAmount = 0;
  if (row.repaymentItem === 'repay_principal') row.interestAmount = 0;
};
const save = async () => {
  const errMap = await CalculationGridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
      return;
    }
  }
  const { visibleData } = CalculationGridApi.grid.getTableData();
  const detailList = cloneDeep(visibleData || []);
  // 新增：校验日期顺序
  const dateValidateResult = validateRepaymentDates(detailList);
  if (dateValidateResult !== true) {
    message.error(dateValidateResult as string);
    return; // 校验失败，终止试算
  }
  if (!isEmpty(detailList)) {
    detailList.forEach((item: any) => {
      item.currentOperationDate = Number(item.currentOperationDate);
    });
  }
  calculationForm.value.detailList = detailList;
  return calculationForm.value;
};
const planningMethodChange = () => {
  if (calculationForm.value.planningMethod === 'manual') {
    calculationForm.value.principalRepaymentMethod = 'regular';
    calculationForm.value.interestRepaymentMethod = 'regular';
  }
};
const totalAmountCalculation = (row: ProjectUseCalculationDetailBO) => {
  // 应还本金(元)
  const principalAmount = new BigNumber(row.principalAmount || 0);
  // 应还利息
  const interestAmount = new BigNumber(row.interestAmount || 0);
  // 当期净现金流 = 应还本金 + 应还利息
  const totalAmount = principalAmount.plus(interestAmount);
  row.totalAmount = totalAmount.decimalPlaces(2, BigNumber.ROUND_HALF_UP).toNumber();
  if (CalculationGridApi.grid) {
    CalculationGridApi.grid.updateFooter();
  }
};
defineExpose({ init, save });
</script>

<template>
  <div>
    <BasicCaption content="还本付息试算" />

    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="还本付息计划规划方式" name="planningMethod">
          <a-radio-group
            v-model:value="calculationForm.planningMethod"
            :options="dictStore.getDictList('FCT_PLANNING_METHOD')"
            @change="planningMethodChange"
            :disabled="calculationType === 'PaymentRecord'"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="测算综合收益率（%/年）" name="xirrRate">
          <a-input-number
            v-model:value="calculationForm.xirrRate"
            disabled
            placeholder="请输入测算综合收益率（%/年）"
            :controls="false"
            class="w-full"
            :precision="2"
          >
            <template #addonAfter>
              <SyncOutlined @click="calculationRate" />
            </template>
          </a-input-number>
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationType !== 'PaymentRecord'">
        <a-form-item label="合同利率（%/年）" name="nominalInterestRate">
          <a-input-number
            v-model:value="calculationForm.nominalInterestRate"
            placeholder="请输入合同利率（%/年）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationType === 'CreditPricing'">
        <a-form-item label="拟用信金额（元）" name="expectedUseAmount">
          <a-input-number
            v-model:value="calculationForm.expectedUseAmount"
            placeholder="请输入拟用信金额（元）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationType === 'Pricing'">
        <a-form-item label="计划融资金额（元）" name="financingAmount">
          <a-input-number
            v-model:value="calculationForm.financingAmount"
            placeholder="请输入计划融资金额（元）"
            :controls="false"
            class="w-full"
            :precision="2"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item
          :label="calculationType !== 'PaymentRecord' ? '预计业务投放日' : '起息日'"
          name="expectedLaunchDate"
        >
          <a-date-picker
            v-model:value="calculationForm.expectedLaunchDate"
            value-format="x"
            class="w-full"
            @change="expectedDateChange"
            :disabled="calculationType === 'PaymentRecord'"
            :placeholder="`请选择${calculationType !== 'PaymentRecord' ? '预计业务投放日' : '起息日'}`"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item
          :label="calculationType !== 'PaymentRecord' ? '预估最后还款日' : '最后还款日'"
          name="expectedDueDate"
        >
          <a-date-picker
            v-model:value="calculationForm.expectedDueDate"
            value-format="x"
            class="w-full"
            @change="expectedDateChange"
            :placeholder="`请选择${calculationType !== 'PaymentRecord' ? '预估最后还款日' : '最后还款日'}`"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.planningMethod === 'automatic'">
        <a-form-item label="还本方式" name="principalRepaymentMethod">
          <a-select
            v-model:value="calculationForm.principalRepaymentMethod"
            :options="dictStore.getDictList('FCT_REPAY_PRINCIPAL_METHOD')"
            :disabled="calculationType === 'PaymentRecord'"
            placeholder="请选择还本方式"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" v-if="calculationForm.planningMethod === 'automatic'">
        <a-form-item label="还息方式" name="interestRepaymentMethod">
          <a-select
            v-model:value="calculationForm.interestRepaymentMethod"
            :options="dictStore.getDictList('FCT_REPAY_INTEREST_METHOD')"
            :disabled="calculationType === 'PaymentRecord'"
            placeholder="请选择还息方式"
          />
        </a-form-item>
      </a-col>
      <template v-if="calculationForm.planningMethod === 'automatic'">
        <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          <a-form-item label="分期还本频次" name="principalPeriod">
            <a-select
              v-model:value="calculationForm.principalPeriod"
              :options="dictStore.getDictList('FCT_FREQUENCY')"
              :disabled="calculationType === 'PaymentRecord'"
              placeholder="请选择分期还本频次"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          <a-form-item label="分期还息频次" name="interestPeriod">
            <a-select
              v-model:value="calculationForm.interestPeriod"
              :options="dictStore.getDictList('FCT_FREQUENCY')"
              :disabled="calculationType === 'PaymentRecord'"
              placeholder="请选择分期还息频次"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="calculationForm.principalRepaymentMethod === 'regular'">
          <a-form-item label="默认当期还本日" name="repayPrincipalDay">
            <a-select
              v-model:value="calculationForm.repayPrincipalDay"
              :options="dictStore.getDictList('FCT_CURRENT_REPAY_DATE')"
              :disabled="calculationType === 'PaymentRecord'"
              placeholder="请选择默认当期还本日"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="calculationForm.interestRepaymentMethod === 'regular'">
          <a-form-item label="默认当期还息日" name="repayInterestDay">
            <a-select
              v-model:value="calculationForm.repayInterestDay"
              :options="dictStore.getDictList('FCT_CURRENT_REPAY_DATE')"
              :disabled="calculationType === 'PaymentRecord'"
              placeholder="请选择默认当期还息日"
            />
          </a-form-item>
        </a-col>
      </template>
      <template v-if="calculationForm.planningMethod === 'manual'">
        <a-col v-bind="colSpan">
          <a-form-item label="还款期数" name="expectedRepayPeriods">
            <a-input-number
              v-model:value="calculationForm.expectedRepayPeriods"
              :disabled="calculationType === 'PaymentRecord'"
              placeholder="请输入还款期数"
              :controls="false"
              class="w-full"
              :precision="0"
            />
          </a-form-item>
        </a-col>
      </template>
      <a-col v-bind="colSpan">
        <a-form-item label="预估计息天数（天）" name="expectedInterestDays">
          <a-input-number
            v-model:value="calculationForm.expectedInterestDays"
            disabled
            placeholder="请输入预估计息天数（天）"
            :controls="false"
            class="w-full"
            :precision="0"
          />
        </a-form-item>
      </a-col>
      <template v-if="calculationForm.planningMethod === 'automatic'">
        <a-col v-bind="colSpan">
          <a-form-item label="预估还款期数">
            <a-input-number
              v-model:value="calculationForm.expectedRepayPeriods"
              disabled
              placeholder="请输入预估还款期数"
              :controls="false"
              class="w-full"
              :precision="0"
            />
          </a-form-item>
        </a-col>
      </template>
    </a-row>
    <CalculationGrid>
      <template #toolbarTools>
        <a-space>
          <a-button danger type="link">*请在修改上方业务参数后点击“开始试算”按钮</a-button>
          <a-button type="primary" @click="calculation('')">开始试算</a-button>
          <a-button type="primary" @click="calculation('interest')">仅更新利息</a-button>
        </a-space>
      </template>
      <template #edit_repayment_item="{ row }">
        <a-select
          v-model:value="row.repaymentItem"
          :options="repaymentOptions"
          :disabled="row.repaymentItemDisabled"
          class="w-full"
          @change="repaymentItemChange(row)"
        />
      </template>
      <template #edit_current_date="{ row }">
        <a-date-picker
          v-model:value="row.currentOperationDate"
          value-format="x"
          class="w-full"
          :allow-clear="false"
          :disabled="row.currentDateDisabled"
        />
      </template>
      <template #edit_principal_amount="{ row }">
        <a-input-number
          :controls="false"
          v-model:value="row.principalAmount"
          :disabled="row.principalAmountDisabled || row.repaymentItem === 'repay_interest'"
          class="w-full"
          :default-value="0"
          :min="0"
          :precision="2"
          @blur="totalAmountCalculation(row)"
        />
      </template>
      <template #edit_interest_amount="{ row }">
        <a-input-number
          :controls="false"
          v-model:value="row.interestAmount"
          :disabled="row.interestAmountDisabled || row.repaymentItem === 'repay_principal'"
          class="w-full"
          :default-value="0"
          :min="0"
          :precision="2"
          @blur="totalAmountCalculation(row)"
        />
      </template>
    </CalculationGrid>
  </div>
</template>
