<script setup lang="ts">
import type { PaymentApplyInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';
import { formatDate, formatMoney } from '@vben/utils';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getContractIdApi, getPaymentApplyInfoApi, getProjectLimitApi } from '#/api';
import { ContractList } from '#/components';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

const emit = defineEmits(['ok', 'register']);
const { WorkflowPreviewModal, useOperationButton, useViewButton, initWorkflow, isWorkflow, isWorkflowLoading } =
  useWorkflowBase();

const OperationButton = useOperationButton();
const ViewButton = useViewButton();
const pageType = ref('detail');
const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: PaymentApplyInfo) => {
  pageType.value = data.pageType ?? 'detail';
  await initWorkflow({ formKey: 'fct_payment_apply', businessKey: data.id });
  applyForm.value = data.id ? await getPaymentApplyInfoApi(data.id as number) : { ...data };
  getContractId({ projectApplyId: applyForm.value.projectCreditApplyId, projectId: applyForm.value.projectId });
  await getProjectLimit({
    projectId: applyForm.value.projectId,
    projectCreditApplyId: applyForm.value.projectCreditApplyId,
  });
};

const getContractId = async (data: { projectCreditApplyId: number; projectId: number }) => {
  const res = await getContractIdApi(data);
  applyForm.value.contractId = res;
};

const getProjectLimit = async (data: { projectCreditApplyId: number; projectId: number }) => {
  const res = await getProjectLimitApi(data);
  const { id: _, ...info } = res;
  applyForm.value = { ...applyForm.value, ...info };
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const applyForm = ref<PaymentApplyInfo>({});
</script>

<template>
  <BasicPopup title="付款申请详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton @success="workflowSuccess" />
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联项目">
          {{ applyForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="付款申请编号">
          {{ applyForm.applyCode }}
        </a-descriptions-item>
        <a-descriptions-item label="付款单位">
          {{ applyForm.payerCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="用信申请金额（元）" v-if="applyForm.projectType === 'comprehensive'">
          {{ formatMoney(applyForm.projectApplyAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="投放金额（元）">
          {{ formatMoney(applyForm.investAmount) }}
        </a-descriptions-item>
        <a-descriptions-item label="付款方式">
          {{ dictStore.formatter(applyForm.paymentMethod, 'FCT_PAYMENT_METHOD') }}
        </a-descriptions-item>
        <a-descriptions-item label="预计投放日期">
          {{ formatDate(applyForm.expectInvestDate) }}
        </a-descriptions-item>
        <a-descriptions-item label="备注">
          {{ applyForm.remarks }}
        </a-descriptions-item>
      </a-descriptions>
      <template v-if="applyForm.projectType === 'comprehensive'">
        <BasicCaption content="授信及额度信息" />
        <a-descriptions v-bind="descriptionsProp" class="mt-4">
          <a-descriptions-item label="授信额度（元）">
            {{ formatMoney(applyForm.creditAmount) }}
          </a-descriptions-item>
          <a-descriptions-item label="授信利率（%/年）">
            {{ formatMoney(applyForm.creditRate) }}
          </a-descriptions-item>
          <a-descriptions-item label="授信企业">
            {{ applyForm.creditCompanyName }}
          </a-descriptions-item>
          <a-descriptions-item label="授信额度类型">
            {{ dictStore.formatter(applyForm.creditType, 'FCT_CREDIT_TYPE') }}
          </a-descriptions-item>
          <a-descriptions-item label="授信批复时间">
            {{ formatDate(applyForm.creditApprovalDate) }}
          </a-descriptions-item>
          <a-descriptions-item label="授信期限（个月）">
            {{ applyForm.creditTerm }}
          </a-descriptions-item>
          <a-descriptions-item label="已用额度（元）">
            {{ formatMoney(applyForm.usedAmount) }}
          </a-descriptions-item>
          <a-descriptions-item label="可用额度（元）">
            {{ formatMoney(applyForm.availableAmount) }}
          </a-descriptions-item>
        </a-descriptions>
      </template>
      <ContractList
        :business-id="applyForm.contractId"
        :business-type="
          applyForm.projectType === 'comprehensive' ? 'FCT_PROJECT_APPLY_CONTRACT_SEAL' : 'FCT_PROJECT_CONTRACT_SEAL'
        "
        :edit-mode="false"
      >
        <template #header>
          <BasicCaption content="盖章合同" class="mb-4" />
        </template>
      </ContractList>
      <BasicCaption content="确认收款单位信息" />
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="收款单位">
          {{ applyForm.payeeCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="收款单位开户行名称">
          {{ applyForm.payeeBankBranch }}
        </a-descriptions-item>
        <a-descriptions-item label="收款单位银行账号">
          {{ applyForm.payeeBankAccount }}
        </a-descriptions-item>
      </a-descriptions>
      <BaseAttachmentList :business-id="applyForm.id" business-type="FCT_PAYMENT_APPLY" />
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style></style>
