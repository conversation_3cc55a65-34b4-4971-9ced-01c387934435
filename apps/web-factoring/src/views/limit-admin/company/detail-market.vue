<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { companyComputeVo } from '#/api';

import { reactive, ref } from 'vue';

import { prompt } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatMoney } from '@vben/utils';

import { message, Table, Textarea } from 'ant-design-vue';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { editCompanyApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import { spanMethod } from '../components/tableSpanUtils';

const emit = defineEmits(['ok', 'register']);

const {
  startWorkflow,
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isProcessInstance,
  isWorkflow,
  isWorkflowLoading,
} = useWorkflowBase();
const pageType = ref('detail');
const ViewButton = useViewButton();

const OperationButton = useOperationButton();

const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const baseFormInfo = reactive<companyComputeVo>({
  cityCode: '',
  cityName: '',
  companyCode: '',
  companyName: '',
  companyRating: '',
  companyType: '',
  districtCode: '',
  districtName: '',
  isSubmit: false,
  marketAdjustFactor: 0,
  marketBasicAmount: 0,
  marketCreditAmount: 0,
  marketCreditScore: 0,
  marketDepreciation: 0,
  marketNetProfit: 0,
  operatingAdjustFactor: 0,
  operatingBasicAmount: 0,
  operatingCreditAmount: 0,
  operatingCreditScore: 0,
  operatingLimitRuleList: [],
  regionBudgetAmount: 0,
  regionCreditAmount: 0,
  regionCreditRatio: 0,
  regionCreditScore: 0,
  regionLimitRuleList: [],
  marketLimitRuleList: [
    {
      quotaCategory: '主体类型',
      quotaName: '公司类型',
      dictType: 'FCT_LIMIT_COMPANY_TYPE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '主体类型',
      quotaName: '所在区域',
      dictType: 'FCT_LIMIT_REGION',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '所有者权益规模（集团）（元）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '资产负债率（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '短期债务/总债务（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: 'EBIT/债务利息',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '财务报表可靠性',
      dictType: 'FCT_LIMIT_FINANCIAL_STATEMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '主体评级',
      dictType: 'FCT_LIMIT_RATING',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '公司治理：按提供相关制度计算',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务经营质量（净资产收益率%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务多样性：包括主营业务、第二增长曲线、财务投资、特许经营权等',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '经营稳定性',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '配合度：配合积极及时，材料提供完整',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '信用记录',
      dictType: 'FCT_LIMIT_CREDIT_HISTORY',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '及时偿还行为',
      dictType: 'FCT_LIMIT_REPAYMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '综合成本接受度',
      quotaName: '成本接受',
      dictType: 'FCT_LIMIT_COST',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '舆情',
      quotaName: '负面舆情：授信对象出现负面舆情,如债务违约，或者高管违法违纪等',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
  adjustSupportingFileId: 0,
  budgetAmount: 0,
  creditAmount: 0,
  creditRatio: 0,
  creditScore: 0,
  limitRuleList: [],
  limitLogList: [],
});
const gridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 160,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '30%',
    },
    {
      field: 'numericalValue',
      title: '数值',
      slots: { default: 'numericalValue' },
      width: '35%',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: baseFormInfo.marketLimitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};

const logColumns: TableColumnType[] = [
  { title: '测算日期', dataIndex: 'operationDate', width: 140 },
  { title: '市场化经营客户测算得分', dataIndex: 'marketCreditScore' },
  { title: '市场化经营客户测算最后结果（元）', dataIndex: 'marketCreditAmount', formatter: 'formatMoney' },
  {
    title: '测算方式',
    dataIndex: 'operationType',
    customRender: ({ value }) => dictStore.formatter(value, 'FCT_LIMIT_OPERATION_TYPE'),
  },
  { title: '操作人', dataIndex: 'operationBy' },
  { title: '调整佐证材料', dataIndex: 'adjustSupportingFileId' },
];
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
// 添加初始化方法
const init = async (data: companyComputeVo) => {
  pageType.value = data.pageType ?? 'detail';
  if (data && Object.keys(data).length > 0) {
    await initWorkflow({ formKey: 'fct_market_company_limit', businessKey: data.id });
    if (data.marketLimitRuleList.length > 0) {
      data.marketLimitRuleList = data.marketLimitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.marketLimitRuleList[index].dictType,
        };
      });
      gridApi.grid?.reloadData(data.marketLimitRuleList);
    }

    Object.assign(baseFormInfo, cloneDeep(data));
  }
};
const state = reactive({
  loading: false,
});
const audit = async (status: number) => {
  if (status === 1) {
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    if (processDefinitionKey) {
      state.loading = true;
      try {
        await editCompanyApi({
          id: baseFormInfo.id as number,
          isPass: status,
          processDefinitionKey,
          startUserSelectAssignees,
        });
        message.success($t('base.resSuccess'));
        emit('ok');
        closePopup();
      } finally {
        state.loading = false;
      }
      return false;
    }
  }
  prompt({
    content: '请输入审核意见：',
    title: '审核意见',
    component: Textarea,
    modelPropName: 'value',
    async beforeClose(scope) {
      if (!scope.isConfirm) return;
      if (!scope.value) {
        message.warning('请输入审核意见');
        return false;
      }
      state.loading = true;
      try {
        await editCompanyApi({
          id: baseFormInfo.id as number,
          isPass: status,
          remark: scope.value,
        });
      } finally {
        state.loading = false;
      }
      return true;
    },
  }).then(() => {
    message.success($t('base.resSuccess'));
    emit('ok');
    closePopup();
  });
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [registerPopup, { closePopup }] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
        <a-space v-else>
          <a-button :loading="state.loading" type="primary" @click="audit(1)">通过</a-button>
          <a-button :loading="state.loading" type="primary" danger @click="audit(0)">驳回</a-button>
        </a-space>
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="企业名称">
          {{ baseFormInfo.companyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="统一社会信用代码">
          {{ baseFormInfo.companyCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="客户类别">
          {{ dictStore.formatter(baseFormInfo.companyType, 'FCT_COMPANY_LIMIT_COMPANY_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="企业评级">
          {{ dictStore.formatter(baseFormInfo.companyRating, 'COMPANY_RATING') }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="市场化经营客户额度测算" />
      <Grid>
        <!-- 评级赋分下拉框 -->
        <template #numericalValue="{ row }">
          <TypographyText v-if="row.dictType !== 'input' && row.dictType !== 'number' && row.dictType !== 'number-10'">
            {{ dictStore.formatter(row.numericalValue, row.dictType) }}
          </TypographyText>
          <TypographyText v-else v-model:value="row.numericalValue">
            {{ row.numericalValue }}
          </TypographyText>
        </template>
      </Grid>
      <BasicCaption content="市场化经营客户额度测算结果" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="市场化经营客户测算得分">
          {{ formatMoney(baseFormInfo.marketCreditScore) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="调整因子">
          {{ formatMoney(baseFormInfo.marketAdjustFactor) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="税后经营净利润（元）">
          {{ formatMoney(baseFormInfo.marketNetProfit) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="折旧与摊销（元）">
          {{ formatMoney(baseFormInfo.marketDepreciation) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="额度计算基数（元）">
          {{ formatMoney(baseFormInfo.marketBasicAmount) || '' }}
        </a-descriptions-item>
        <a-descriptions-item label="最后结果（元）">
          {{ formatMoney(baseFormInfo.marketCreditAmount) || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="市场化经营客户额度测算历史记录" />
      <Table
        class="center-table w-full"
        row-key="id"
        style="margin-top: 10px"
        size="small"
        bordered
        :columns="logColumns"
        :data-source="baseFormInfo.limitLogList"
        :pagination="false"
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'adjustSupportingFileId'">
            <BaseFilePickList v-model="record.adjustSupportingFileId" :edit-mode="false" />
          </template>
        </template>
      </Table>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
