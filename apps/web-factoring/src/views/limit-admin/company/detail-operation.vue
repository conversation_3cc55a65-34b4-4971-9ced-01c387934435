<script setup lang="ts">
import type { TableColumnType } from 'ant-design-vue';

import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { companyComputeVo } from '#/api';

import { reactive, ref } from 'vue';

import { prompt } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { cloneDeep, formatMoney } from '@vben/utils';

import { message, Table, Textarea, TypographyText } from 'ant-design-vue';

import { BaseFilePickList } from '#/adapter/base-ui';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { editCompanyApi } from '#/api';
import { useWorkflowBase } from '#/composables/useWorkflowBase';

import { spanMethod } from '../components/tableSpanUtils';

const emit = defineEmits(['ok', 'register']);
const {
  startWorkflow,
  WorkflowPreviewModal,
  useOperationButton,
  useViewButton,
  initWorkflow,
  isProcessInstance,
  isWorkflow,
  isWorkflowLoading,
} = useWorkflowBase();
const pageType = ref('detail');
const ViewButton = useViewButton();
const dictStore = useDictStore();
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const baseFormInfo = reactive<companyComputeVo>({
  cityCode: '',
  cityName: '',
  companyCode: '',
  companyName: '',
  companyRating: '',
  companyType: '',
  districtCode: '',
  districtName: '',
  isSubmit: false,
  marketAdjustFactor: 0,
  marketBasicAmount: 0,
  marketCreditAmount: 0,
  marketCreditScore: 0,
  marketDepreciation: 0,
  marketNetProfit: 0,
  operatingAdjustFactor: 0,
  operatingBasicAmount: 0,
  operatingCreditAmount: 0,
  operatingCreditScore: 0,
  operatingLimitRuleList: [
    {
      quotaCategory: '区域情况',
      quotaName: '人口（万人）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政平衡率(%)',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: 'GDP增速（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政实力(%)',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '区域利差',
      dictType: 'FCT_LIMIT_INTEREST_RATE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '所有者权益规模（集团）（元）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '资产负债率（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '短期债务/总债务（%）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: 'EBIT/债务利息',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象财务指标',
      quotaName: '财务报表可靠性',
      dictType: 'FCT_LIMIT_FINANCIAL_STATEMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '主体评级',
      dictType: 'FCT_LIMIT_RATING',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '公司治理：按提供相关制度计算',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务经营质量（净资产收益率）',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '业务多样性：包括主营业务、第二增长曲线、财务投资、特许经营权等',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '授信对象非财务指标',
      quotaName: '经营稳定性',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '配合度：配合积极及时，材料提供完整',
      dictType: 'number-10',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '信用记录',
      dictType: 'FCT_LIMIT_CREDIT_HISTORY',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '履约情况',
      quotaName: '及时偿还行为',
      dictType: 'FCT_LIMIT_REPAYMENT',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '综合成本接受度',
      quotaName: '成本接受',
      dictType: 'FCT_LIMIT_COST',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '舆情',
      quotaName: '负面舆情：授信对象出现负面舆情,如债务违约，或者高管违法违纪等',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
  regionBudgetAmount: 0,
  regionCreditAmount: 0,
  regionCreditRatio: 0,
  regionCreditScore: 0,
  regionLimitRuleList: [],
  marketLimitRuleList: [],
  adjustSupportingFileId: 0,
  budgetAmount: 0,
  creditAmount: 0,
  creditRatio: 0,
  creditScore: 0,
  limitRuleList: [
    {
      quotaCategory: '区域情况',
      quotaName: '人口（万人）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政平衡率（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: 'GDP增速（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '财政实力（%）',
      dictType: 'input',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '区域利差',
      dictType: 'FCT_LIMIT_INTEREST_RATE',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '区域情况',
      quotaName: '特色产业：有关政策明确支持的产业、区域、企业、项目，或辖区内有上市公司总部或者开设工厂',
      dictType: 'number',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '过往合作履约情况',
      quotaName: '过往合作履约情况',
      dictType: 'FCT_LIMIT_PREVIOUS',
      numericalValue: '',
      remark: '',
    },
    {
      quotaCategory: '负面舆情',
      quotaName: '负面舆情：区域内出现国有发债主体的债券违约事件或者频繁的非标融资、银行借款等债务逾期风险事件',
      dictType: 'FCT_LIMIT_SENTIMENT',
      numericalValue: '',
      remark: '',
    },
  ],
  limitLogList: [],
});
const OperationButton = useOperationButton();
// 添加初始化方法
const init = async (data: any) => {
  pageType.value = data.pageType ?? 'detail';
  if (data && Object.keys(data).length > 0) {
    await initWorkflow({ formKey: 'fct_operation_company_limit', businessKey: data.id });
    if (data.companyType === 'region_customer') {
      baseFormInfo.companyName = data.companyName;
      baseFormInfo.companyCode = data.companyCode;
    }
    if (data.limitRuleList.length > 0) {
      data.limitRuleList = data.limitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.limitRuleList[index].dictType,
        };
      });
      gridAreaApi.grid?.reloadData(data.limitRuleList);
    }
    if (data.operatingLimitRuleList.length > 0) {
      data.operatingLimitRuleList = data.operatingLimitRuleList.map((value, index) => {
        return {
          ...value,
          dictType: baseFormInfo.operatingLimitRuleList[index].dictType,
        };
      });
      gridApi.grid?.reloadData(data.operatingLimitRuleList);
    }
    Object.assign(baseFormInfo, cloneDeep(data));
  }
};

const gridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 160,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '30%',
    },
    {
      field: 'numericalValue',
      title: '数值',
      slots: { default: 'numericalValue' },
      width: '35%',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: baseFormInfo.operatingLimitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};

const AreaGridOptions: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  keepSource: true,
  columns: [
    {
      field: 'quotaCategory',
      title: '指标分类',
      width: 140,
    },
    {
      field: 'quotaName',
      title: '指标名称',
      width: '43%',
    },
    {
      field: 'contractType',
      title: '数值',
      slots: { default: 'contractType-select' },
      width: '25%',
    },
    {
      field: 'remark',
      title: '备注',
    },
  ],
  data: baseFormInfo.limitRuleList,
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  spanMethod: (params: any) => spanMethod(params, 'quotaCategory'),
};

const logColumns: TableColumnType[] = [
  { title: '测算日期', dataIndex: 'operationDate', width: 140 },
  { title: '区域保理额度测算得分', dataIndex: 'regionCreditScore' },
  { title: '地区保理授信额度（元）', dataIndex: 'regionCreditAmount' },
  { title: '区域经营性客户测算得分', dataIndex: 'operatingCreditScore' },
  { title: '区域经营性客户额度（元）', dataIndex: 'operatingCreditAmount' },
  { title: '最后授信额度测算结果（元）', dataIndex: 'creditAmount' },
  {
    title: '测算方式',
    dataIndex: 'operationType',
    customRender: ({ value }) => dictStore.formatter(value, 'FCT_LIMIT_OPERATION_TYPE'),
  },
  { title: '操作人', dataIndex: 'operationBy' },
  { title: '调整佐证材料', dataIndex: 'adjustSupportingFileId' },
];

const state = reactive({
  loading: false,
});
const audit = async (status: number) => {
  if (status === 1) {
    const { processDefinitionKey, startUserSelectAssignees } = await startWorkflow();
    if (processDefinitionKey) {
      state.loading = true;
      try {
        await editCompanyApi({
          ...baseFormInfo,
          isPass: status,
          processDefinitionKey,
          startUserSelectAssignees,
        });
        message.success($t('base.resSuccess'));
        emit('ok');
        closePopup();
      } finally {
        state.loading = false;
      }
      return false;
    }
  }
  prompt({
    content: '请输入审核意见：',
    title: '审核意见',
    component: Textarea,
    modelPropName: 'value',
    async beforeClose(scope) {
      if (!scope.isConfirm) return;
      if (!scope.value) {
        message.warning('请输入审核意见');
        return false;
      }
      state.loading = true;
      try {
        await editCompanyApi({
          ...baseFormInfo,
          isPass: status,
          remark: scope.value,
        });
      } finally {
        state.loading = false;
      }
      return true;
    },
  }).then(() => {
    message.success($t('base.resSuccess'));
    emit('ok');
    closePopup();
  });
};
const workflowSuccess = () => {
  emit('ok');
  closePopup();
};
const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });
const [AreaGrid, gridAreaApi] = useVbenVxeGrid({ gridOptions: AreaGridOptions });
const [registerPopup, { closePopup }] = usePopupInner((data) => init(data));
</script>

<template>
  <BasicPopup v-bind="$attrs" title="详情" @register="registerPopup">
    <template #insertToolbar>
      <div v-if="pageType === 'audit'" v-loading="isWorkflowLoading">
        <OperationButton v-if="isProcessInstance" @success="workflowSuccess" />
        <a-space v-else>
          <a-button :loading="state.loading" type="primary" @click="audit(1)">通过</a-button>
          <a-button :loading="state.loading" type="primary" danger @click="audit(0)">驳回</a-button>
        </a-space>
      </div>
      <ViewButton v-if="isWorkflow" />
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="企业名称">
          {{ baseFormInfo.companyName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="统一社会信用代码">
          {{ baseFormInfo.companyCode || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="客户类别">
          {{ dictStore.formatter(baseFormInfo.companyType, 'FCT_COMPANY_LIMIT_COMPANY_TYPE') }}
        </a-descriptions-item>
        <a-descriptions-item label="企业评级">
          {{ dictStore.formatter(baseFormInfo.companyRating, 'COMPANY_RATING') }}
        </a-descriptions-item>
        <a-descriptions-item label="地市区县">
          {{ baseFormInfo.cityName || '-' }}/{{ baseFormInfo.districtName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="地方一般公共预算收入（元）">
          {{ formatMoney(baseFormInfo.regionBudgetAmount) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="额度计算基数（元）">
          {{ formatMoney(baseFormInfo.operatingBasicAmount) || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="区域保理额度测算" />
      <AreaGrid>
        <!-- 评级赋分下拉框 -->
        <template #contractType-select="{ row }">
          <TypographyText v-if="row.dictType !== 'input' && row.dictType !== 'number'">
            {{ dictStore.formatter(row.numericalValue, row.dictType) }}
          </TypographyText>
          <TypographyText v-else v-model:value="row.numericalValue">
            {{ row.numericalValue }}
          </TypographyText>
        </template>
      </AreaGrid>
      <BasicCaption content="地区额度测算结果" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="地区保理额度测算得分">
          {{ formatMoney(baseFormInfo.regionCreditScore) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="区域授信比例（%）">
          {{ formatMoney(baseFormInfo.regionCreditRatio) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="地区保理授信额度（元）">
          {{ formatMoney(baseFormInfo.regionCreditAmount) || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="区域经营性客户额度测算" />
      <Grid>
        <!-- 评级赋分下拉框 -->
        <template #numericalValue="{ row }">
          <TypographyText v-if="row.dictType !== 'input' && row.dictType !== 'number' && row.dictType !== 'number-10'">
            {{ dictStore.formatter(row.numericalValue, row.dictType) }}
          </TypographyText>
          <TypographyText v-else v-model:value="row.numericalValue">
            {{ row.numericalValue }}
          </TypographyText>
        </template>
      </Grid>
      <BasicCaption content="区域经营性客户额度测算结果" />
      <a-descriptions class="mt-4" v-bind="descriptionsProp">
        <a-descriptions-item label="区域经营性客户测算得分">
          {{ formatMoney(baseFormInfo.operatingCreditScore) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="调整因子">
          {{ formatMoney(baseFormInfo.operatingAdjustFactor) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="额度计算基数（元）">
          {{ formatMoney(baseFormInfo.operatingBasicAmount) || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="最后结果（元）">
          {{ formatMoney(baseFormInfo.operatingCreditAmount) || '-' }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="区域经营性客户额度测算历史记录" />
      <Table
        class="center-table w-full"
        row-key="id"
        style="margin-top: 10px"
        size="small"
        bordered
        :columns="logColumns"
        :data-source="baseFormInfo.limitLogList"
        :pagination="false"
        :scroll="{ x: 'max-content' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'adjustSupportingFileId'">
            <BaseFilePickList v-model="record.adjustSupportingFileId" :edit-mode="false" />
          </template>
        </template>
      </Table>
    </div>
    <template v-if="pageType === 'audit'">
      <WorkflowPreviewModal />
    </template>
  </BasicPopup>
</template>

<style scoped>
.text-with-newline {
  white-space: pre-line;

  /* 或 pre 或 pre-wrap */
}
</style>
